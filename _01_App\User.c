/* ****************************
 * Project description:
 *
 * A Project empty template file
 *
 * Author: 创新基地 -> 2023 Bo
 *
 * Creation Date: 2023/04/07
 *
 * Update date: 2023/04/07
 * ****************************/

/* ***************************** Include & Define Part     	*****************************
 * 头文件声明及宏定义区
 * */
#include "User.h"

/* ***************************** Variable definition Part   *****************************
 * 变量定义区
 * */

#define windowsize 10  //均值滤波窗口值
#define INPUT_SIGNAL_AMPLITUDE 8.0


float A[100];
uint8_t is_calibrated[100];
// ADC采样序列
extern uint32_t ADCData[];

// 菜单序号变量
uint8_t MenuSign = 0;




/* ***************************** Main Part                  *****************************
 * 主函数区
 * */
void User_main(void)
{ 
	uint16_t count = 0;
	
	// 初始化全部
	Init_All();
	
	// 显示主界面
	Disp_Main();

	while(1)
	{
//		dds[0].fre = 200000;
//		sendData(dds[0],0);
		switch( MenuSign )
		{
			case 0: 
				if( Ps2KeyValue != KeyValue_Null ) 	//在未选中菜单时有按键按下
					Change_Menu( Ps2KeyValue );				//根据按键改变菜单界面
				  Disp_Main();
				;break;
			case 1: MenuHaddler_1(); break;
			case 2: MenuHaddler_2(); break;
			case 3: MenuHaddler_3(); break;
			case 4: MenuHaddler_4(); break;
			default: break;
		}
		
		delay_ms(10);
	}
}

void GPIO_Change_Init()//继电器初始化  PC12  PC10    
{
	GPIO_InitTypeDef  GPIO_InitStructure;
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOC, ENABLE);
	GPIO_InitStructure.GPIO_Pin =  GPIO_Pin_11|GPIO_Pin_12;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_2MHz;
	GPIO_InitStructure.GPIO_Mode=GPIO_Mode_OUT;
	GPIO_InitStructure.GPIO_OType=GPIO_OType_PP;
	GPIO_InitStructure.GPIO_PuPd  = GPIO_PuPd_NOPULL;
	GPIO_Init(GPIOC,&GPIO_InitStructure);
}

/* ***************************** Initialization Part        *****************************
 * 初始化函数区
 * */

// 初始化全部 参数：无
void Init_All()
{
	LCD_Clear(Black);
	
	// 初始化ADC
	//User_ADC_Init( 1 );//双通道采集
	
	GPIO_Change_Init();
	// 初始化DAC
	//User_DAC_Init();
	
	// 初始化AD8370
	//AD8370_Init();
	
	// 初始化DAC8562
	//DAC8562_Init( 0 );
	
	// 初始化PGA2310
	//PGA2310_Init();
	
	// 初始化IIC通信
	//IIC_Init();
	
	// 初始化SPI通信
	//SPI_GPIO_Init( 1 );
	
	// 初始化串口(用于控制9958和9851输出)
	Init_Uart(115200);
	// DDS结构体初始化
	DDSDataInit();
	
	// 初始化ADS1256
	ADS1256_Init();

	//初始化flash
	//W25Q64_Init();
}

/* ***************************** Display Part               *****************************
 * 显示函数区
 * */

// 显示界面框架 参数：无
void Disp_Main()
{
	uint8_t count;
	
	// Show title
	OS_String_Show( 400 - 32 * 4 , 16 ,32 , 0 , TitleStr );
	
	// Draw line
	LCD_Appoint_Clear( 0 ,64 , 800 , 64 + 8 ,White );
	LCD_Appoint_Clear( 0 ,480 - 32 - 8 , 800 , 480 - 32 ,White );
	LCD_Appoint_Clear( 250 , 64 + 8 , 250 + 2 , 480 - 32 - 8 , White );
	//LCD_Appoint_Clear( 300 + 2 , 96 + 64 * 4 , 800 , 96 + 64 * 4 + 2 , White );
	
	// Show model version str
	OS_String_Show( 32 , 480 - 16 - 8 ,16 , 0 , ModelVerStr );
	// Show user version str
	OS_String_Show( 632 , 480 - 16 - 8 ,16 , 0 , UserVerStr );
	
	// Disp menu
	for( count = 1 ; count < MenuChoiceNum + 1 ; count ++ )
		OS_String_Show( 32 , 32 + 64 * count ,32 , 0 , "—" );
	for( count = 0 ; count < MenuChoiceNum ; count ++ )
	{
		switch( count )
		{
			case 0: OS_String_Show( 80 , 96 ,32 , 0 , Menu1Choice1 );break;
			case 1: OS_String_Show( 80 , 96 + 64 ,32 , 0 , Menu1Choice2 );break;
			case 2: OS_String_Show( 80 , 96 + 64 * 2 ,32 , 0 , Menu1Choice3 );break;
			case 3: OS_String_Show( 80 , 96 + 64 * 3 ,32 , 0 , Menu1Choice4 );break;
			default: break;
		}
	}
	
//	switch (DDS_Switch)
//	{
//		case 0:
//			OS_String_Show(350,96+64,32,1,"关");
//			OS_String_Show(350+32*7,96+64+16,16,1,"按0，1开关信号源");
//		  break;
//		case 1:
//			OS_String_Show(350,96+64,32,1,"开");
//			OS_String_Show(350+32*7,96+64+16,16,1,"按0，1开关信号源");
//			break;
//	}
}

// 输出数值 参数：1>位置；2>数值；3>输出格式,如"数值%0.0f"
void Show_Val( uint8_t location , float value , char *str )
{
	
	if( location > 0 && location <= 10 )
		OS_Num_Show( 250 + 64 , 96 + 32 * ( location - 1 ) ,32 , 1 , value , str );
	else if( location > 10 && location <= 20 )
		OS_Num_Show( 500 + 64 , 96 + 32 * ( location - 11 ) ,32 , 1 , value , str );
	else
		OS_String_Show( 250 + 64 , 96 ,32 , 1 , "ERROR" );
	
}

// 切换菜单页 参数：1>菜单序号(1~5)
void Change_Menu( uint8_t menu_sign )
{
	uint8_t count;
	
	// 清除显示区域
	LCD_Appoint_Clear( 250 +2 , 64 + 8 , 800 , 480 - 32 - 8 , Black );
	
	for( count = 1 ; count < MenuChoiceNum + 1 ; count ++ )
		OS_String_Show( 32 , 32 + 64 * count ,32 , 1 , "—" );
	
	if( menu_sign > 0 && menu_sign <= MenuChoiceNum )
		OS_String_Show( 32 , 32 + 64 * menu_sign ,32 , 1 , "◆" );
	else
		menu_sign = 0;
	
	Ps2KeyValue = KeyValue_Null;
	MenuSign = menu_sign;
}


float linear(uint32_t input_data,uint32_t low_lever,uint32_t high_lever,uint32_t low_slope,uint32_t high_slope)
{
	return low_slope + (input_data-input_data)/(high_lever-low_lever)*(high_slope-low_slope);
}
/* ***************************** Menu Handler Part     	 	 	*****************************
 * 菜单执行函数区
 */

// 菜单1执行函数 参数：无
void MenuHaddler_1()
{
	uint32_t data_I;//未经过转换的24位数据
	uint32_t data_Q;
	float value_I;//经过转换的数据
	float value_Q;
	float A_current;
	uint32_t paper_count;//测量得出的纸张数目
	Ps2KeyValue = KeyValue_Null;
	dds[0].phase = 0;
	dds[1].phase = 90;	//输出正交正弦信号
	dds[0].range = dds[1].range = 0.3200;
	dds[0].fre = dds[1].fre= 1400000;
	sendData(dds[0],0); 
	delay_ms(100);
	sendData(dds[1],1); 
	Line_fitRead(A,100);
	while( Ps2KeyValue != KeyValue_Back )
	{
		data_I = Moving_Average_Filter(1,50);
		data_Q = Moving_Average_Filter(1,50);
		value_I = Get_Val(data_I);
		value_Q = Get_Val(data_Q);
		A_current = (2.0f * sqrt(value_I * value_I + value_Q * value_Q)) / INPUT_SIGNAL_AMPLITUDE;
		paper_count = calculate_paper_count(A_current);
		Show_Val( 1 ,value_I , "测得电压值1：%.5f       " );
		Show_Val( 2 ,value_Q , "测得电压值2：%.5f       " );
		Show_Val( 3 ,paper_count , "测得纸张数目：%.0f       " );
	}
	
	Change_Menu( 0 );
}

// 菜单2执行函数 参数：无
void MenuHaddler_2()
{
	uint32_t data_I;//未经过转换的24位数据
	uint32_t data_Q;
	float value_I;//经过转换的数据
	float value_Q;
	float A_current;
	uint8_t mode = 0;
	uint8_t num = KeyValue_Null;
	Ps2KeyValue = KeyValue_Null;
	dds[0].phase = 0;
	dds[1].phase = 90;	//输出正交正弦信号
	dds[0].range = dds[1].range = 0.3200;
	dds[0].fre = dds[1].fre= 1400000;
	sendData(dds[0],0); 
	delay_ms(100);
	sendData(dds[1],1); 
	Ps2KeyValue = KeyValue_Null;
	while( Ps2KeyValue != KeyValue_Back )
	{
		switch(mode)
		{
			case 0:
				// OS_String_Show( 32 , 32 + 64 ,32 , 1 , "1.进入校准模式    " );
				// OS_String_Show( 32 , 32 + 64*2 ,32 , 1 , "2.清空flash数据    " );
				Show_Val( 1 ,1.0 , "1.进入校准模式       " );
				Show_Val( 2 ,1.0 , "2.清空flash数据       " );
				if(Ps2KeyValue == KeyValue_1)
				{
					mode = 1;
				}
				if(Ps2KeyValue == KeyValue_2)
				{
					mode = 2;
				}
			break;
			case 1:
				// OS_String_Show( 32 , 32 + 64 ,32 , 1 , "校准模式中         " );
				// OS_String_Show( 32 , 32 + 64*2 ,32 , 1 , "                    " );
				Show_Val( 1 ,1.0 , "校准模式中             " );
				Show_Val( 2 ,1.0 , "                        " );
				data_I = Moving_Average_Filter(1,50);
				data_Q = Moving_Average_Filter(1,50);
				value_I = Get_Val(data_I);
				value_Q = Get_Val(data_Q);
				A_current = (2.0f * sqrt(value_I * value_I + value_Q * value_Q)) / INPUT_SIGNAL_AMPLITUDE;
				// OS_String_Show( 32 , 32 + 64*2 ,32 , 1 , "请按键输入纸张数       " );
				Show_Val( 3 ,1.0 , "请按键输入纸张数       " );
				num = (uint8_t)PS2_ReadNum(num);
				// OS_String_Show( 32 , 32 + 64*2 ,32 , 1 , "                       " );
				Show_Val( 3 ,1.0 , "                         " );
				Ps2KeyValue = KeyValue_Null;
				A[num] = A_current;
				is_calibrated[num] = 1;
				Line_fitWrite(A,100);
				if(Ps2KeyValue == KeyValue_Enter)
				{
					mode = 0;
				}
				Ps2KeyValue = KeyValue_Null;
			break;
			case 2:
				// 擦除校准标志所在扇区(16MB起始位置)
				W25Q64_Erase_Sector(16*1024*1024/4096);  // 计算扇区号：16MB/4KB = 4096
				// 擦除数据所在扇区(17MB起始位置)
				W25Q64_Erase_Sector(17*1024*1024/4096);  // 计算扇区号：17MB/4KB = 4352
				while(Ps2KeyValue != KeyValue_Enter)
				Show_Val( 2 ,1.0 , "擦除成功                 " );
				mode = 0;
			break;
		}
		
	}
	
	Change_Menu( 0 );
}


// 菜单3执行函数 参数：无
void MenuHaddler_3()
{
	Ps2KeyValue = KeyValue_Null;
	while( Ps2KeyValue != KeyValue_Back )
	{
		
	}
	
	Change_Menu( 0 );
}

		

// 菜单4执行函数 参数：无
void MenuHaddler_4()
{
	Ps2KeyValue = KeyValue_Null;
	while( Ps2KeyValue != KeyValue_Back )
	{
		
	}
	
	Change_Menu( 0 );
}



/* ***************************** Custom Function Part       *****************************
 * 其他自定义函数区
 */

void Line_fitWrite(float A[],int n)
{
	uint8_t i = 0;
	uint8_t AbyteArray[sizeof(float)];    
	//将32位的数据转换成8位数据进行存储
	for(i=0;i<n;i++)
	{
		memcpy(AbyteArray, &A[i], sizeof(float)); 
		W25Q64_Write(&is_calibrated[i],1024*1024*16 + i*sizeof(uint8_t),sizeof(uint8_t));
		W25Q64_Write(AbyteArray,1024*1024*17 + i*sizeof(float),sizeof(float));

	}
	  
}

void Line_fitRead(float A[],int n)
{
	uint8_t i = 0;
	uint8_t AbyteArray[sizeof(float)]; 
	for(i=0;i<n;i++)
	{
		W25Q64_Read(&is_calibrated[i],1024*1024*16 + i*sizeof(uint8_t),sizeof(uint8_t));
		W25Q64_Read(AbyteArray,1024*1024*17 + i*sizeof(float) ,sizeof(float));
		memcpy(&A[i],AbyteArray , sizeof(float)); 
	}
}

//寻找进行线性插值的区间
void find_calibrated_interval(float A_current, int *p_lower_idx, int *p_upper_idx) 
{
    int valid_points[100];
    int valid_count = 0;
    
    // 1. 收集所有已校准点的索引
    for(int i = 0; i <= 99; i++) {
        if(is_calibrated[i]) {
            valid_points[valid_count++] = i;
        }
    }
    
    // 如果校准点少于2个，无法插值
    if(valid_count < 2) {
        return ;
    }
    
    // 2. 处理边界情况
    
    // 2.1 检查A_current是否大于所有校准点的A值
    float max_A = -1.0f;
    int max_A_idx = -1;
    
    for(int i = 0; i < valid_count; i++) {
        int idx = valid_points[i];
        if(A[idx] > max_A) {
            max_A = A[idx];
            max_A_idx = idx;
        }
    }
    
    if(A_current >= max_A) {
        // A_current大于或等于所有校准点的最大A值（通常对应0张纸）
        *p_lower_idx = max_A_idx;
        *p_upper_idx = max_A_idx;
        return ;
    }
    
    // 2.2 检查A_current是否小于所有校准点的A值
    float min_A = 100.0f;
    int min_A_idx = -1;
    
    for(int i = 0; i < valid_count; i++) {
        int idx = valid_points[i];
        if(A[idx] < min_A) {
            min_A = A[idx];
            min_A_idx = idx;
        }
    }
    
    if(A_current <= min_A) {
        // A_current小于或等于所有校准点的最小A值（通常对应最大纸张数）
        *p_lower_idx = min_A_idx;
        *p_upper_idx = min_A_idx;
        return ;
    }
    
    // 3. 找到两个最适合插值的已校准点
    
    int best_lower = -1, best_upper = -1;
    float min_dist = 100.0f;
    
    for(int i = 0; i < valid_count; i++) {
        for(int j = i+1; j < valid_count; j++) {
            int idx1 = valid_points[i];
            int idx2 = valid_points[j];
            
            // 确保idx1 < idx2（纸张数量较小的在前）
            if(idx1 > idx2) {
                int temp = idx1;
                idx1 = idx2;
                idx2 = temp;
            }
            
            float A1 = A[idx1];
            float A2 = A[idx2];
            
            // 查找A_current是否位于A1和A2之间
            if((A_current >= A1 && A_current <= A2) || (A_current <= A1 && A_current >= A2)) {
                // 记录纸张数量较少的为lower_idx，较多的为upper_idx
                *p_lower_idx = idx1;
                *p_upper_idx = idx2;
                return ;
            }
            
            // 如果不在区间内，记录距离最小的点对
            float dist = fabsf(A_current - A1) + fabsf(A_current - A2);
            if(dist < min_dist) {
                min_dist = dist;
                best_lower = idx1;
                best_upper = idx2;
            }
        }
    }
    
    // 如果没有找到包含A_current的区间，使用距离最小的点对
    if(best_lower != -1 && best_upper != -1) {
        *p_lower_idx = best_lower;
        *p_upper_idx = best_upper;
        return ;
    }
    
    return ;
}

//进行线性插值，测出纸张数量
int calculate_paper_count(float A_current)
{
    int lower_idx = -1, upper_idx = -1;
    
    // 1. 调用你的函数找到适合插值的区间
    find_calibrated_interval(A_current, &lower_idx, &upper_idx);
    
    // 2. 检查是否找到有效的区间
    if(lower_idx == -1 || upper_idx == -1) {
        // 没有找到有效区间，返回错误值或默认值
        return -1;
    }
    
    // 3. 如果两个索引相同（边界情况），直接返回对应的纸张数量
    if(lower_idx == upper_idx) {
        return lower_idx; // 索引直接对应纸张数量
    }
    
    // 4. 执行线性插值
    float A_lower = A[lower_idx];  // 纸张数量较少的点对应的A值
    float A_upper = A[upper_idx];  // 纸张数量较多的点对应的A值
    
    // 用纸张数量作为y值，A值作为x值进行插值
    // 注意：在你的设计中，索引就是对应的纸张数量
    float paper_count_float;
    
    // 防止除以零
    if(fabsf(A_upper - A_lower) < 1e-6) {
        // A值几乎相同，取两个点的平均纸张数量
        paper_count_float = (lower_idx + upper_idx) / 2.0f;
    } else {
        // 执行标准的线性插值
        // 公式: y = y1 + (x - x1) * (y2 - y1) / (x2 - x1)
        // 这里: y是纸张数量，x是A值
        paper_count_float = (float)lower_idx + 
                            (A_current - A_lower) * 
                            ((float)(upper_idx - lower_idx) / (A_upper - A_lower));
    }
    
    // 5. 四舍五入到最近的整数
    int paper_count = (int)(paper_count_float + 0.5f);
    
    // 6. 确保结果在有效范围内
    if(paper_count < 0) {
        paper_count = 0;
    } else if(paper_count > 99) { // 假设最大支持99张
        paper_count = 99;
    }
    
    return paper_count;
}

float User_Abs( float val )
{
	if( val >= 0 )
		return val;
	else 
		return -val;
}


// 修正相位范围(FFT直接做相位差得到范围为-360~360)
float User_FixPhase( float pha )
{
	while( 1 )
	{
		if( pha < -180 )
			pha = pha + 360;
		else if( pha > 180 )
			pha = pha - 360;
		else
			return pha;
	}
}


// 获取信号幅频相频 参数：1>采样电压数据(返回幅频数据)；2>返回相频数据； 返回：1>基波频点下标
uint16_t Get_FreSpectrum( float vol_data[ADCDataLength] , float pha_data[ADCDataLength] )
{
	uint16_t count , index = 0;		// 计数器；基波频点下标
	complex data[ADCDataLength];	// 复数数据组
	float vpp_max = 0;						// 最大幅值
	
	// 将电压数据转化为复数
	for( count = 0 ; count < ADCDataLength ; count ++ )	
	{
		data[count].real = vol_data[count];	// 实部为电压值
		data[count].imag = 0;								// 虚部为0
	}
	
	// 对数据做FFT
	//fft( ADCDataLength , data );												
	fft_process(data);
	// 对前半数据取模以及求出相角
	for( count = 0 ; count < ADCDataLength / 2 ; count ++ )	
	{
		vol_data[count] = sqrt(data[count].real * data[count].real + data[count].imag * data[count].imag);
		pha_data[count] = atan2( data[count].imag , data[count].real ) * 180 / PI;
	}
	
	// 提取直流量
	vol_data[0] = vol_data[0] / ADCDataLength;	// 第一个数据除以采样点数为直流分量
	
	// 提取谐波分量
	for( count = 1 ; count < ADCDataLength / 2 ; count ++ )
		vol_data[count] = vol_data[count] / ADCDataLength * 4;	// 对应频点模值除以采样点数乘以4得到幅值
	
	// 寻找基波频点(除直流外幅值最大的频率点)
	for( count = 1 ; count < ADCDataLength / 2 ; count ++ )
	{
		if( vol_data[count] > vpp_max )
		{
			vpp_max = vol_data[count];
			index = count;
		}
	}
	
	return index;
}

void meanFilter(float* input, float* output, uint16_t length, uint8_t windowSize)
{
    // 参数检查
    if (input == NULL || output == NULL || length == 0) {
        return ;
    }
    
    // 窗口大小必须小于数组长度
    if (windowSize > length) {
        return ;
    }
    
    // 计算半窗口大小(左侧)
    uint8_t halfWindowLeft = windowSize / 2;
    
    // 计算半窗口大小(右侧) - 处理偶数窗口的情况
    uint8_t halfWindowRight = (windowSize % 2 == 0) ? (windowSize / 2 - 1) : (windowSize / 2);
    
    // 处理边缘情况：开头的元素
    for (uint16_t i = 0; i < halfWindowLeft; i++) {
        float sum = 0.0f;
        uint8_t count = 0;
        
        // 只能使用当前点及其后面的点
        for (uint16_t j = 0; j <= i + halfWindowRight; j++) {
            sum += input[j];
            count++;
        }
        
        output[i] = sum / count;
    }
    
    // 处理中间的元素
    for (uint16_t i = halfWindowLeft; i < length - halfWindowRight; i++) {
        float sum = 0.0f;
        
        // 使用完整窗口
        for (int16_t j = i - halfWindowLeft; j <= i + halfWindowRight; j++) {
            sum += input[j];
        }
        
        output[i] = sum / windowSize;
    }
    
    // 处理边缘情况：结尾的元素
    for (uint16_t i = length - halfWindowRight; i < length; i++) {
        float sum = 0.0f;
        uint8_t count = 0;
        
        // 只能使用当前点及其前面的点
        for (uint16_t j = i - halfWindowLeft; j < length; j++) {
            sum += input[j];
            count++;
        }
        
        output[i] = sum / count;
    }
    
}

// 获取信号幅值和相位
void User_GetSignalInf( float vpp[2] , float *pha_dif )
{
	uint16_t index = 0;
	uint32_t count;
	float hanningCoef;
	float vol1[ADCDataLength] = {0} , vol2[ADCDataLength] = {0};
	float t_pha[ADCDataLength] = {0};
	float temp1[ADCDataLength] = {0};//采集的是temp数组中，然后进行均值滤波
	float temp2[ADCDataLength] = {0};


	// 获取双通道电压数据
	Get_ACVol( temp1 , temp2 );//ADC采样获取数据
	//Get_ACVol( vol1 , vol2 );//ADC采样获取数据
	
	//进行四点均值滤波，并将滤波得到的数据放到vol数组中
	meanFilter(temp1 , vol1 , ADCDataLength,windowsize);
	meanFilter(temp2 , vol2 , ADCDataLength,windowsize);
	
	// for( count = 0 ; count < ADCDataLength ; count ++ )
	// {
	// 	hanningCoef = 0.5 * (1.0 - cosf(2.0*PI*count/(ADCDataLength-1)));
	// 	// 低16位为ADC1的数据
	// 	vol1[count] *= hanningCoef;
	// 	// 高16位为ADC2的数据
	// 	vol2[count] *= hanningCoef;
	// }
	for( count = 0 ; count < ADCDataLength/8 ; count ++ )
	{
	WaveData[count] = (uint16_t)(vol1[8*count]*5 / 3.3 * 0x0fff + 0.5f);
	}
	TIM_Cmd(TIM6 , ENABLE);
	// 获取通道一输入信号的基波频点幅值和相位
	index = Get_FreSpectrum( vol1 , t_pha );
	vpp[0] = vol1[index];
	*pha_dif = t_pha[index];
	// for( count = 0 ; count < ADCDataLength/8 ; count ++ )
	// {
	// WaveData[count] = (uint16_t)(vol1[8*count] / 3.3 * 0x0fff + 0.5f);
	// }
	// 通道二
	index = Get_FreSpectrum( vol2 , t_pha );
	vpp[1] = vol2[index];
	*pha_dif = *pha_dif - t_pha[index];		// 直接做相位差
	
	// 修正范围
	*pha_dif = User_FixPhase( *pha_dif );
}


//从1256读电压函数 参数：1>通道1；2>通道2；3>模式(0为单通道测量，1为两通道差分测量) 返回：电压值( -5v 至 +5v )
float ADS1256_ReadVol( uint8_t ch_1 , uint8_t ch_2 , uint8_t mode ) 
{
	float adc_data;
	float vol;
	float base_vol = 4.9896;;
	
	if( ch_1 < 8 )
		ch_1 = ch_1 * 16;
	else 
		ch_1 = 0;
	
	if( mode == 0 )
		ch_2 = ADS1256_MUXN_AINCOM;
	
	adc_data = ADS1256ReadData( ch_1 | ch_2 );
	
	adc_data = adc_data > 0x7fffff ? adc_data - 0xffffff : adc_data;
	
	vol = (float)adc_data / 0x7fffff * base_vol;
	
	return vol;
}




//从PS2输入数字 参数：1>未按键时返回的数值 返回：键入的数值
float PS2_ReadNum( float num )
{
	uint8_t count = 0;
	uint8_t dec_sign = 0;
	float temp_num = 0;
	
	if( Ps2KeyValue <= KeyValue_9)
	{
		TIM_Cmd( TIM6 , DISABLE );
		
		LCD_Appoint_Clear( 332 , 96 + 64 * 4 , 750 + 1 , 480 - 32 - 8 , Black );
		OS_Rect_Draw( 332 , 96 + 64 * 4 , 750 , 96 + 64 * 5 , 1 , White );
		
		while( Ps2KeyValue <= KeyValue_Point)
		{
			if( dec_sign == 0 )
			{
				if( Ps2KeyValue == KeyValue_Point )
				{
					dec_sign = 1;
					count = 0;
				}
				else
					temp_num = temp_num * 10 + Ps2KeyValue;
			}
			else
				temp_num = temp_num + (float)Ps2KeyValue / pow( 10 , count );
			
			OS_Num_Show( 332 + 16 , 96 + 64 * 4 + 16 , 32 , 1 ,temp_num , "-> %.2f" );
			
			count ++;
			Ps2KeyValue = KeyValue_Null;
			while( Ps2KeyValue == KeyValue_Null );
		}
		
		Ps2KeyValue = KeyValue_Null;
		LCD_Appoint_Clear( 332 , 96 + 64 * 4 , 750 + 1 , 480 - 32 - 8 , Black );
	}
	else
		temp_num = num;
	
	Ps2KeyValue = KeyValue_Null;
	
	return temp_num;
}




//AD9958电压设置
void AD9958_Vcc(uint16_t v_output)
{
	switch (v_output)
	{
		//0.1v
		case 1:
			dds[0].range = 0.0310;
			sendData(dds[0],0); 
			delay_ms(1);
		break;
		//0.3v
		case 2:
			//dds[1].range = 0.0960;
			dds[0].range = 0.0620;
			sendData(dds[0],0); 
			delay_ms(1);
		break;
		//0.2v
		case 3:
			dds[0].range = 0.0930;
			//dds[1].range = 0.0420;
			sendData(dds[0],0); 
			delay_ms(1);
		break;

		case 4:
			dds[0].range = 0.1240;
			//dds[1].range = 0.0420;
			sendData(dds[0],0); 
			delay_ms(1);
		break;

		case 5:
			dds[0].range = 0.1550;
			//dds[1].range = 0.0420;
			sendData(dds[0],0); 
			delay_ms(1);
		break;
	}
	   
}









/* ***************************** IRQHandler Part    	     	*****************************
 * 中断执行函数区
 */


/* ***************************** 						END 	   	     	*****************************/










// float Calculate_Smooth_Impedance(float raw_z, uint8_t current_gear) {
//     float result = raw_z;
    
//     // 1.3k和36k档位的重叠区域处理
//     if(current_gear == Gear_1200 && raw_z > 1800 && raw_z < 2000) {
//         // 在1800-2000欧姆的重叠区域
//         float weight = (raw_z - 1800) / 200.0f;  // 0到1的权重
        
//         // 临时切换到高档位进行测量
//         uint8_t original_gear = Gear_sign;
//         User_SetGear(Gear_15k);
        
//         // 获取高档位的测量值
//         float vpp_high[2];
//         float pha_dif_high;
//         User_GetSignalInf(vpp_high, &pha_dif_high);
//         float z_high = Rref_15k * vpp_high[0] / vpp_high[1];
        
//         // 恢复原档位
//         User_SetGear(original_gear);
        
//         // 加权平均
//         result = raw_z * (1 - weight) + z_high * weight;
//     }
    
//     // ... 类似处理其他重叠区域
    
//     return result;
// }


// float Calibrate_Impedance(float raw_z, uint8_t gear) {
//     float calibrated_z = raw_z;
    
//     switch(gear) {
//         case Gear_51:
//             // 51欧姆档位校准曲线
//             calibrated_z = raw_z * 1.05;  // 简单示例，实际需测量多点确定
//             break;
            
//         case Gear_1200:
//             // 1300欧姆档位分段校准
//             if(raw_z >= 2400 && raw_z <= 2700)
//                 calibrated_z = raw_z * 1.35;
//             else if(raw_z > 2700 && raw_z <= 3000)
//                 calibrated_z = raw_z * (1.35 + (raw_z-2700)/(3000-2700)*(1.40-1.35));
//             else if(raw_z > 3000 && raw_z <= 3200)
//                 calibrated_z = raw_z * 1.40;
//             else if(raw_z > 3200 && raw_z <= 8500)
//                 calibrated_z = raw_z * (1.40 + (raw_z-3200)/(8500-3200)*(0.55-1.40));
//             break;
            
//         case Gear_15k:
//             // 36k欧姆档位分段校准
//             if(raw_z >= 8500 && raw_z <= 11800)
//                 calibrated_z = raw_z * 0.55;
//             // ... 其他分段
//             break;
            
//         case Gear_750k:
//             // 1000k欧姆档位校准
//             calibrated_z = raw_z * 0.95;  // 示例值
//             break;
//     }
    
//     return calibrated_z;
// }

