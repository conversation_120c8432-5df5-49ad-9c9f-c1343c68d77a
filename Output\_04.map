Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    app_touch.o(i.TouchStart_Judge) refers to app_touch.o(.bss) for .bss
    app_touch.o(i.Touch_Clear) refers to app_touch.o(.bss) for .bss
    app_touch.o(i.Touch_Judge) refers to app_touch.o(i.Touch_Clear) for Touch_Clear
    app_touch.o(i.Touch_Judge) refers to app_touch.o(.bss) for .bss
    app_touch.o(i.Touch_main) refers to os_cpu.o(i.OSTimeDly) for OSTimeDly
    app_touch.o(i.Touch_main) refers to drive_touch.o(i.TouchRead) for TouchRead
    app_touch.o(i.Touch_main) refers to app_touch.o(.data) for .data
    app_touch.o(i.Touch_main) refers to app_touch.o(.bss) for .bss
    app_led.o(i.LED_main) refers to app_led.o(i.LED_Control) for LED_Control
    app_led.o(i.LED_main) refers to os_cpu.o(i.OSTimeDly) for OSTimeDly
    app_led.o(i.LED_main) refers to app_led.o(.data) for .data
    user_adc.o(i.ADC_DMA_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user_adc.o(i.ADC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    user_adc.o(i.ADC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    user_adc.o(i.ADC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_ClearFlag) for DMA_ClearFlag
    user_adc.o(i.ADC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_ITConfig) for DMA_ITConfig
    user_adc.o(i.ADC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    user_adc.o(i.ADC_DMA_Init) refers to user_adc.o(.bss) for .bss
    user_adc.o(i.ADC_DMA_NVIC_Init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    user_adc.o(i.ADC_DMA_NVIC_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    user_adc.o(i.ADC_DualMode_RegSimult_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    user_adc.o(i.ADC_DualMode_RegSimult_Init) refers to stm32f4xx_adc.o(i.ADC_CommonInit) for ADC_CommonInit
    user_adc.o(i.ADC_DualMode_RegSimult_Init) refers to stm32f4xx_adc.o(i.ADC_Init) for ADC_Init
    user_adc.o(i.ADC_DualMode_RegSimult_Init) refers to stm32f4xx_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    user_adc.o(i.ADC_DualMode_RegSimult_Init) refers to stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd) for ADC_DMARequestAfterLastTransferCmd
    user_adc.o(i.ADC_DualMode_RegSimult_Init) refers to stm32f4xx_adc.o(i.ADC_DMACmd) for ADC_DMACmd
    user_adc.o(i.ADC_DualMode_RegSimult_Init) refers to stm32f4xx_adc.o(i.ADC_Cmd) for ADC_Cmd
    user_adc.o(i.ADC_Mode_Independent_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    user_adc.o(i.ADC_Mode_Independent_Init) refers to stm32f4xx_adc.o(i.ADC_CommonInit) for ADC_CommonInit
    user_adc.o(i.ADC_Mode_Independent_Init) refers to stm32f4xx_adc.o(i.ADC_Init) for ADC_Init
    user_adc.o(i.ADC_Mode_Independent_Init) refers to stm32f4xx_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    user_adc.o(i.ADC_Mode_Independent_Init) refers to stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd) for ADC_DMARequestAfterLastTransferCmd
    user_adc.o(i.ADC_Mode_Independent_Init) refers to stm32f4xx_adc.o(i.ADC_DMACmd) for ADC_DMACmd
    user_adc.o(i.ADC_Mode_Independent_Init) refers to stm32f4xx_adc.o(i.ADC_Cmd) for ADC_Cmd
    user_adc.o(i.ADC_TIM3_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    user_adc.o(i.ADC_TIM3_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    user_adc.o(i.ADC_TIM3_Init) refers to stm32f4xx_tim.o(i.TIM_SelectOutputTrigger) for TIM_SelectOutputTrigger
    user_adc.o(i.ADC_TIM3_Init) refers to stm32f4xx_tim.o(i.TIM_UpdateDisableConfig) for TIM_UpdateDisableConfig
    user_adc.o(i.ADC_TIM3_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    user_adc.o(i.ADC_TIM3_Init) refers to user_adc.o(i.Set_SamplingFre) for Set_SamplingFre
    user_adc.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_ClearFlag) for DMA_ClearFlag
    user_adc.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    user_adc.o(i.DMA2_Stream0_IRQHandler) refers to user_adc.o(.data) for .data
    user_adc.o(i.Get_ACVol) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    user_adc.o(i.Get_ACVol) refers to user_adc.o(.data) for .data
    user_adc.o(i.Get_ACVol) refers to user_adc.o(.bss) for .bss
    user_adc.o(i.Get_DCVol) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    user_adc.o(i.Get_DCVol) refers to user_adc.o(.bss) for .bss
    user_adc.o(i.Set_SamplingFre) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    user_adc.o(i.User_ADC_GPIO_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user_adc.o(i.User_ADC_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    user_adc.o(i.User_ADC_Init) refers to user_adc.o(i.User_ADC_GPIO_Init) for User_ADC_GPIO_Init
    user_adc.o(i.User_ADC_Init) refers to user_adc.o(i.ADC_DualMode_RegSimult_Init) for ADC_DualMode_RegSimult_Init
    user_adc.o(i.User_ADC_Init) refers to user_adc.o(i.ADC_DMA_Init) for ADC_DMA_Init
    user_adc.o(i.User_ADC_Init) refers to user_adc.o(i.ADC_DMA_NVIC_Init) for ADC_DMA_NVIC_Init
    user_adc.o(i.User_ADC_Init) refers to user_adc.o(i.ADC_TIM3_Init) for ADC_TIM3_Init
    user_adc.o(i.User_ADC_Init) refers to user_adc.o(i.ADC_Mode_Independent_Init) for ADC_Mode_Independent_Init
    user.o(i.AD9958_Vcc) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    user.o(i.AD9958_Vcc) refers to drive_communication.o(i.sendData) for sendData
    user.o(i.AD9958_Vcc) refers to delay.o(i.delay_ms) for delay_ms
    user.o(i.AD9958_Vcc) refers to drive_communication.o(.bss) for dds
    user.o(i.ADS1256_ReadVol) refers to drive_ads1256.o(i.ADS1256ReadData) for ADS1256ReadData
    user.o(i.Change_Menu) refers to tft_lcd.o(i.LCD_Appoint_Clear) for LCD_Appoint_Clear
    user.o(i.Change_Menu) refers to os_ui.o(i.OS_String_Show) for OS_String_Show
    user.o(i.Change_Menu) refers to drive_ps2.o(.data) for Ps2KeyValue
    user.o(i.Change_Menu) refers to user.o(.data) for .data
    user.o(i.Disp_Main) refers to os_ui.o(i.OS_String_Show) for OS_String_Show
    user.o(i.Disp_Main) refers to tft_lcd.o(i.LCD_Appoint_Clear) for LCD_Appoint_Clear
    user.o(i.GPIO_Change_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user.o(i.GPIO_Change_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    user.o(i.Get_FreSpectrum) refers to drive_fft.o(i.fft_process) for fft_process
    user.o(i.Get_FreSpectrum) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user.o(i.Get_FreSpectrum) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    user.o(i.Get_FreSpectrum) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    user.o(i.Get_FreSpectrum) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    user.o(i.Get_FreSpectrum) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    user.o(i.Get_FreSpectrum) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    user.o(i.Init_All) refers to tft_lcd.o(i.LCD_Clear) for LCD_Clear
    user.o(i.Init_All) refers to user.o(i.GPIO_Change_Init) for GPIO_Change_Init
    user.o(i.Init_All) refers to drive_communication.o(i.Init_Uart) for Init_Uart
    user.o(i.Init_All) refers to drive_communication.o(i.DDSDataInit) for DDSDataInit
    user.o(i.Init_All) refers to drive_ads1256.o(i.ADS1256_Init) for ADS1256_Init
    user.o(i.Line_fitRead) refers to w25q64.o(i.W25Q64_Read) for W25Q64_Read
    user.o(i.Line_fitRead) refers to user.o(.bss) for .bss
    user.o(i.Line_fitWrite) refers to w25q64.o(i.W25Q64_Write) for W25Q64_Write
    user.o(i.Line_fitWrite) refers to user.o(.bss) for .bss
    user.o(i.MenuHaddler_1) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    user.o(i.MenuHaddler_1) refers to drive_communication.o(i.sendData) for sendData
    user.o(i.MenuHaddler_1) refers to delay.o(i.delay_ms) for delay_ms
    user.o(i.MenuHaddler_1) refers to user.o(i.Line_fitRead) for Line_fitRead
    user.o(i.MenuHaddler_1) refers to drive_ads1256.o(i.Moving_Average_Filter) for Moving_Average_Filter
    user.o(i.MenuHaddler_1) refers to drive_ads1256.o(i.Get_Val) for Get_Val
    user.o(i.MenuHaddler_1) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user.o(i.MenuHaddler_1) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    user.o(i.MenuHaddler_1) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    user.o(i.MenuHaddler_1) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    user.o(i.MenuHaddler_1) refers to user.o(i.calculate_paper_count) for calculate_paper_count
    user.o(i.MenuHaddler_1) refers to user.o(i.Show_Val) for Show_Val
    user.o(i.MenuHaddler_1) refers to user.o(i.Change_Menu) for Change_Menu
    user.o(i.MenuHaddler_1) refers to drive_ps2.o(.data) for Ps2KeyValue
    user.o(i.MenuHaddler_1) refers to drive_communication.o(.bss) for dds
    user.o(i.MenuHaddler_1) refers to user.o(.bss) for .bss
    user.o(i.MenuHaddler_2) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    user.o(i.MenuHaddler_2) refers to drive_communication.o(i.sendData) for sendData
    user.o(i.MenuHaddler_2) refers to delay.o(i.delay_ms) for delay_ms
    user.o(i.MenuHaddler_2) refers to user.o(i.Show_Val) for Show_Val
    user.o(i.MenuHaddler_2) refers to drive_ads1256.o(i.Moving_Average_Filter) for Moving_Average_Filter
    user.o(i.MenuHaddler_2) refers to drive_ads1256.o(i.Get_Val) for Get_Val
    user.o(i.MenuHaddler_2) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user.o(i.MenuHaddler_2) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    user.o(i.MenuHaddler_2) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    user.o(i.MenuHaddler_2) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    user.o(i.MenuHaddler_2) refers to user.o(i.PS2_ReadNum) for PS2_ReadNum
    user.o(i.MenuHaddler_2) refers to user.o(i.Line_fitWrite) for Line_fitWrite
    user.o(i.MenuHaddler_2) refers to w25q64.o(i.W25Q64_Erase_Sector) for W25Q64_Erase_Sector
    user.o(i.MenuHaddler_2) refers to user.o(i.Change_Menu) for Change_Menu
    user.o(i.MenuHaddler_2) refers to drive_ps2.o(.data) for Ps2KeyValue
    user.o(i.MenuHaddler_2) refers to drive_communication.o(.bss) for dds
    user.o(i.MenuHaddler_2) refers to user.o(.bss) for .bss
    user.o(i.MenuHaddler_3) refers to user.o(i.Change_Menu) for Change_Menu
    user.o(i.MenuHaddler_3) refers to drive_ps2.o(.data) for Ps2KeyValue
    user.o(i.MenuHaddler_4) refers to user.o(i.Change_Menu) for Change_Menu
    user.o(i.MenuHaddler_4) refers to drive_ps2.o(.data) for Ps2KeyValue
    user.o(i.PS2_ReadNum) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    user.o(i.PS2_ReadNum) refers to tft_lcd.o(i.LCD_Appoint_Clear) for LCD_Appoint_Clear
    user.o(i.PS2_ReadNum) refers to os_ui.o(i.OS_Rect_Draw) for OS_Rect_Draw
    user.o(i.PS2_ReadNum) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    user.o(i.PS2_ReadNum) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    user.o(i.PS2_ReadNum) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    user.o(i.PS2_ReadNum) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user.o(i.PS2_ReadNum) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    user.o(i.PS2_ReadNum) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    user.o(i.PS2_ReadNum) refers to os_ui.o(i.OS_Num_Show) for OS_Num_Show
    user.o(i.PS2_ReadNum) refers to drive_ps2.o(.data) for Ps2KeyValue
    user.o(i.Show_Val) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user.o(i.Show_Val) refers to os_ui.o(i.OS_Num_Show) for OS_Num_Show
    user.o(i.Show_Val) refers to os_ui.o(i.OS_String_Show) for OS_String_Show
    user.o(i.User_GetSignalInf) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    user.o(i.User_GetSignalInf) refers to user_adc.o(i.Get_ACVol) for Get_ACVol
    user.o(i.User_GetSignalInf) refers to user.o(i.meanFilter) for meanFilter
    user.o(i.User_GetSignalInf) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user.o(i.User_GetSignalInf) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    user.o(i.User_GetSignalInf) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    user.o(i.User_GetSignalInf) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    user.o(i.User_GetSignalInf) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    user.o(i.User_GetSignalInf) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    user.o(i.User_GetSignalInf) refers to user.o(i.Get_FreSpectrum) for Get_FreSpectrum
    user.o(i.User_GetSignalInf) refers to user.o(i.User_FixPhase) for User_FixPhase
    user.o(i.User_GetSignalInf) refers to user_dac.o(.bss) for WaveData
    user.o(i.User_main) refers to user.o(i.Init_All) for Init_All
    user.o(i.User_main) refers to user.o(i.Disp_Main) for Disp_Main
    user.o(i.User_main) refers to user.o(i.Change_Menu) for Change_Menu
    user.o(i.User_main) refers to user.o(i.MenuHaddler_1) for MenuHaddler_1
    user.o(i.User_main) refers to user.o(i.MenuHaddler_2) for MenuHaddler_2
    user.o(i.User_main) refers to user.o(i.MenuHaddler_3) for MenuHaddler_3
    user.o(i.User_main) refers to user.o(i.MenuHaddler_4) for MenuHaddler_4
    user.o(i.User_main) refers to delay.o(i.delay_ms) for delay_ms
    user.o(i.User_main) refers to user.o(.data) for .data
    user.o(i.User_main) refers to drive_ps2.o(.data) for Ps2KeyValue
    user.o(i.calculate_paper_count) refers to user.o(i.find_calibrated_interval) for find_calibrated_interval
    user.o(i.calculate_paper_count) refers to user.o(.bss) for .bss
    user.o(i.find_calibrated_interval) refers to user.o(.bss) for .bss
    main.o(i.OS_Init) refers to os_cpu.o(i.System_init) for System_init
    main.o(i.OS_Init) refers to drive_gpio.o(i.LED_Init) for LED_Init
    main.o(i.OS_Init) refers to drive_ps2.o(i.PS2_Keyboard_Init) for PS2_Keyboard_Init
    main.o(i.OS_Init) refers to os_ui.o(i.OS_LCD_Init) for OS_LCD_Init
    main.o(i.main) refers to os_cpu.o(i.Task_Create) for Task_Create
    main.o(i.main) refers to main.o(i.OS_Init) for OS_Init
    main.o(i.main) refers to os_cpu.o(i.OS_Start) for OS_Start
    main.o(i.main) refers to main.o(.bss) for .bss
    main.o(i.main) refers to user.o(i.User_main) for User_main
    main.o(i.main) refers to drive_ps2.o(i.MyPs2KeyScan) for MyPs2KeyScan
    main.o(i.main) refers to app_led.o(i.LED_main) for LED_main
    os_cpu.o(i.OSGetHighRdy) refers to os_cpu.o(.data) for .data
    os_cpu.o(i.OSTaskRecovery) refers to core.o(CODE) for OS_CPU_SR_Save
    os_cpu.o(i.OSTaskRecovery) refers to os_cpu.o(i.OSSetPrioRdy) for OSSetPrioRdy
    os_cpu.o(i.OSTaskRecovery) refers to os_cpu.o(i.OS_Sched) for OS_Sched
    os_cpu.o(i.OSTaskRecovery) refers to os_cpu.o(.data) for .data
    os_cpu.o(i.OSTaskRecovery) refers to os_cpu.o(.bss) for .bss
    os_cpu.o(i.OSTaskSuspend) refers to core.o(CODE) for OS_CPU_SR_Save
    os_cpu.o(i.OSTaskSuspend) refers to os_cpu.o(i.OSDelPrioRdy) for OSDelPrioRdy
    os_cpu.o(i.OSTaskSuspend) refers to os_cpu.o(i.OS_Sched) for OS_Sched
    os_cpu.o(i.OSTaskSuspend) refers to os_cpu.o(.data) for .data
    os_cpu.o(i.OSTaskSuspend) refers to os_cpu.o(.bss) for .bss
    os_cpu.o(i.OSTimeDly) refers to core.o(CODE) for OS_CPU_SR_Save
    os_cpu.o(i.OSTimeDly) refers to os_cpu.o(i.OSDelPrioRdy) for OSDelPrioRdy
    os_cpu.o(i.OSTimeDly) refers to os_cpu.o(i.OS_Sched) for OS_Sched
    os_cpu.o(i.OSTimeDly) refers to os_cpu.o(.data) for .data
    os_cpu.o(i.OSTimeDly) refers to os_cpu.o(.bss) for .bss
    os_cpu.o(i.OS_Sched) refers to core.o(CODE) for OS_CPU_SR_Save
    os_cpu.o(i.OS_Sched) refers to os_cpu.o(i.OSGetHighRdy) for OSGetHighRdy
    os_cpu.o(i.OS_Sched) refers to os_cpu.o(.data) for .data
    os_cpu.o(i.OS_Sched) refers to os_cpu.o(.bss) for .bss
    os_cpu.o(i.OS_SchedLock) refers to core.o(CODE) for OS_CPU_SR_Save
    os_cpu.o(i.OS_SchedLock) refers to os_cpu.o(.data) for .data
    os_cpu.o(i.OS_SchedUnlock) refers to core.o(CODE) for OS_CPU_SR_Save
    os_cpu.o(i.OS_SchedUnlock) refers to os_cpu.o(.data) for .data
    os_cpu.o(i.OS_Start) refers to os_cpu.o(i.Task_Create) for Task_Create
    os_cpu.o(i.OS_Start) refers to os_cpu.o(i.OSGetHighRdy) for OSGetHighRdy
    os_cpu.o(i.OS_Start) refers to core.o(CODE) for OSStartHighRdy
    os_cpu.o(i.OS_Start) refers to os_cpu.o(.data) for .data
    os_cpu.o(i.OS_Start) refers to os_cpu.o(.bss) for .bss
    os_cpu.o(i.OS_Start) refers to os_cpu.o(i.OS_IDLE_Task) for OS_IDLE_Task
    os_cpu.o(i.SysTick_Handler) refers to core.o(CODE) for OS_CPU_SR_Save
    os_cpu.o(i.SysTick_Handler) refers to os_cpu.o(i.OSSetPrioRdy) for OSSetPrioRdy
    os_cpu.o(i.SysTick_Handler) refers to os_cpu.o(i.OS_Sched) for OS_Sched
    os_cpu.o(i.SysTick_Handler) refers to os_cpu.o(.data) for .data
    os_cpu.o(i.SysTick_Handler) refers to os_cpu.o(.bss) for .bss
    os_cpu.o(i.System_init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    os_cpu.o(i.System_init) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    os_cpu.o(i.System_init) refers to os_cpu.o(.data) for .data
    os_cpu.o(i.Task_Create) refers to os_cpu.o(i.OSSetPrioRdy) for OSSetPrioRdy
    os_cpu.o(i.Task_Create) refers to os_cpu.o(i.Task_End) for Task_End
    os_cpu.o(i.Task_Create) refers to os_cpu.o(.bss) for .bss
    os_ui.o(i.OS_BackColor_Set) refers to tft_lcd.o(.data) for BackColor
    os_ui.o(i.OS_Char_Show) refers to os_ui.o(i.OS_Point_Draw) for OS_Point_Draw
    os_ui.o(i.OS_Char_Show) refers to fonts.o(.constdata) for asc2_3216
    os_ui.o(i.OS_Char_Show) refers to tft_lcd.o(.data) for TextColor
    os_ui.o(i.OS_Char_Show) refers to fonts.o(.constdata) for asc2_1608
    os_ui.o(i.OS_Char_Show) refers to fonts.o(.constdata) for asc2_2412
    os_ui.o(i.OS_Circle_Draw) refers to tft_lcd.o(i.LCD_DrawCircleS) for LCD_DrawCircleS
    os_ui.o(i.OS_Circle_Draw) refers to tft_lcd.o(i.LCD_DrawCircle) for LCD_DrawCircle
    os_ui.o(i.OS_Circle_Draw) refers to tft_lcd.o(.data) for TextColor
    os_ui.o(i.OS_Font_Show) refers to os_ui.o(i.OS_HzMat_Get) for OS_HzMat_Get
    os_ui.o(i.OS_Font_Show) refers to os_ui.o(i.OS_Point_Draw) for OS_Point_Draw
    os_ui.o(i.OS_Font_Show) refers to tft_lcd.o(.data) for TextColor
    os_ui.o(i.OS_HzMat_Get) refers to w25q64.o(i.W25Q64_Read) for W25Q64_Read
    os_ui.o(i.OS_HzMat_Get) refers to fontupd.o(.bss) for ftinfo
    os_ui.o(i.OS_LCD_Clear) refers to tft_lcd.o(i.LCD_Clear) for LCD_Clear
    os_ui.o(i.OS_LCD_Init) refers to tft_lcd.o(i.TFT_LCD_Init) for TFT_LCD_Init
    os_ui.o(i.OS_LCD_Init) refers to tft_lcd.o(i.LCD_Clear) for LCD_Clear
    os_ui.o(i.OS_LCD_Init) refers to fontupd.o(i.font_init) for font_init
    os_ui.o(i.OS_LCD_Init) refers to os_ui.o(i.OS_String_Show) for OS_String_Show
    os_ui.o(i.OS_LCD_Init) refers to tft_lcd.o(.data) for TextColor
    os_ui.o(i.OS_Line_Draw) refers to os_ui.o(i.OS_Point_Draw) for OS_Point_Draw
    os_ui.o(i.OS_Num_Show) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    os_ui.o(i.OS_Num_Show) refers to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    os_ui.o(i.OS_Num_Show) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    os_ui.o(i.OS_Num_Show) refers to _printf_pad.o(.text) for _printf_pre_padding
    os_ui.o(i.OS_Num_Show) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    os_ui.o(i.OS_Num_Show) refers to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    os_ui.o(i.OS_Num_Show) refers to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    os_ui.o(i.OS_Num_Show) refers to printf2.o(x$fpl$printf2) for _printf_fp_hex
    os_ui.o(i.OS_Num_Show) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    os_ui.o(i.OS_Num_Show) refers to noretval__2sprintf.o(.text) for __2sprintf
    os_ui.o(i.OS_Num_Show) refers to os_ui.o(i.OS_String_Show) for OS_String_Show
    os_ui.o(i.OS_Picture_Draw) refers to tft_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    os_ui.o(i.OS_Picture_Draw) refers to tft_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    os_ui.o(i.OS_Picture_Draw) refers to tft_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    os_ui.o(i.OS_Picture_Draw) refers to tft_lcd.o(.data) for TextColor
    os_ui.o(i.OS_Picture_Draw) refers to os_ui.o(.data) for .data
    os_ui.o(i.OS_Point_Draw) refers to tft_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    os_ui.o(i.OS_Point_Draw) refers to tft_lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    os_ui.o(i.OS_Point_Draw) refers to tft_lcd.o(i.LCD_WriteRAM) for LCD_WriteRAM
    os_ui.o(i.OS_Rect_Draw) refers to tft_lcd.o(i.LCD_DrawRectS) for LCD_DrawRectS
    os_ui.o(i.OS_Rect_Draw) refers to os_ui.o(i.OS_Line_Draw) for OS_Line_Draw
    os_ui.o(i.OS_Rect_Draw) refers to tft_lcd.o(.data) for TextColor
    os_ui.o(i.OS_String_Show) refers to os_ui.o(i.OS_Font_Show) for OS_Font_Show
    os_ui.o(i.OS_String_Show) refers to os_ui.o(i.OS_Char_Show) for OS_Char_Show
    os_ui.o(i.OS_TextColor_Set) refers to tft_lcd.o(.data) for TextColor
    os_ui.o(i.OS_Wave_Draw) refers to os_ui.o(i.OS_Wave_Windows_Show) for OS_Wave_Windows_Show
    os_ui.o(i.OS_Wave_Draw) refers to os_ui.o(i.OS_Wave_Line_Show) for OS_Wave_Line_Show
    os_ui.o(i.OS_Wave_Line_Show) refers to os_ui.o(i.OS_Line_Draw) for OS_Line_Draw
    os_ui.o(i.OS_Wave_Line_Show) refers to os_ui.o(i.OS_Point_Draw) for OS_Point_Draw
    os_ui.o(i.OS_Wave_Line_Show) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    os_ui.o(i.OS_Wave_Line_Show) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    os_ui.o(i.OS_Wave_Line_Show) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    os_ui.o(i.OS_Wave_Line_Show) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    os_ui.o(i.OS_Wave_Line_Show) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    os_ui.o(i.OS_Wave_Line_Show) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    os_ui.o(i.OS_Wave_Windows_Show) refers to os_ui.o(i.OS_Rect_Draw) for OS_Rect_Draw
    os_ui.o(i.OS_Wave_Windows_Show) refers to os_ui.o(i.OS_Line_Draw) for OS_Line_Draw
    os_malloc.o(i.my_mem_free) refers to os_malloc.o(.data) for .data
    os_malloc.o(i.my_mem_free) refers to os_malloc.o(.constdata) for .constdata
    os_malloc.o(i.my_mem_init) refers to os_malloc.o(i.mymemset) for mymemset
    os_malloc.o(i.my_mem_init) refers to os_malloc.o(.constdata) for .constdata
    os_malloc.o(i.my_mem_init) refers to os_malloc.o(.data) for .data
    os_malloc.o(i.my_mem_malloc) refers to os_malloc.o(.data) for .data
    os_malloc.o(i.my_mem_malloc) refers to os_malloc.o(.constdata) for .constdata
    os_malloc.o(i.my_mem_perused) refers to os_malloc.o(.constdata) for .constdata
    os_malloc.o(i.my_mem_perused) refers to os_malloc.o(.data) for .data
    os_malloc.o(i.myfree) refers to core.o(CODE) for OS_CPU_SR_Save
    os_malloc.o(i.myfree) refers to os_malloc.o(i.my_mem_free) for my_mem_free
    os_malloc.o(i.myfree) refers to os_malloc.o(.data) for .data
    os_malloc.o(i.mymalloc) refers to core.o(CODE) for OS_CPU_SR_Save
    os_malloc.o(i.mymalloc) refers to os_malloc.o(i.my_mem_malloc) for my_mem_malloc
    os_malloc.o(i.mymalloc) refers to os_malloc.o(.data) for .data
    os_malloc.o(i.myrealloc) refers to core.o(CODE) for OS_CPU_SR_Save
    os_malloc.o(i.myrealloc) refers to os_malloc.o(i.my_mem_malloc) for my_mem_malloc
    os_malloc.o(i.myrealloc) refers to os_malloc.o(i.mymemcpy) for mymemcpy
    os_malloc.o(i.myrealloc) refers to os_malloc.o(i.myfree) for myfree
    os_malloc.o(i.myrealloc) refers to os_malloc.o(.data) for .data
    os_malloc.o(.data) refers to os_malloc.o(i.my_mem_init) for my_mem_init
    os_malloc.o(.data) refers to os_malloc.o(i.my_mem_perused) for my_mem_perused
    os_malloc.o(.data) refers to os_malloc.o(.bss) for mem1base
    os_malloc.o(.data) refers to os_malloc.o(.ARM.__AT_0x68000000) for mem2base
    os_malloc.o(.data) refers to os_malloc.o(.ARM.__AT_0x10000000) for mem3base
    os_malloc.o(.data) refers to os_malloc.o(.bss) for mem1mapbase
    os_malloc.o(.data) refers to os_malloc.o(.ARM.__AT_0x680F0000) for mem2mapbase
    os_malloc.o(.data) refers to os_malloc.o(.ARM.__AT_0x1000F000) for mem3mapbase
    core.o(CODE) refers to os_cpu.o(.data) for CPU_ExceptStkBase
    core.o(CODE) refers to os_cpu.o(.data) for p_TCB_Cur
    delay.o(i.delay_ms) refers to os_cpu.o(i.OSTimeDly) for OSTimeDly
    delay.o(i.delay_ms) refers to delay.o(i.delay_us) for delay_us
    delay.o(i.delay_ms) refers to os_cpu.o(.data) for OS_Running
    delay.o(i.delay_us) refers to os_cpu.o(i.OS_SchedLock) for OS_SchedLock
    delay.o(i.delay_us) refers to os_cpu.o(i.OS_SchedUnlock) for OS_SchedUnlock
    delay.o(i.delay_us) refers to os_cpu.o(.data) for fac_us
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART1_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for .data
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for .bss
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart.o(i.uart_init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    drive_gpio.o(i.GPIO_Data_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_gpio.o(i.GPIO_Data_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_gpio.o(i.GPIO_Key_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_gpio.o(i.GPIO_Key_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_gpio.o(i.GPIO_POW_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_gpio.o(i.GPIO_POW_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_gpio.o(i.GPIO_POW_Init) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    drive_gpio.o(i.LED_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_gpio.o(i.LED_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_GetCmdStatus) for DMA_GetCmdStatus
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_SetCurrDataCounter) for DMA_SetCurrDataCounter
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_CommonInit) for ADC_CommonInit
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_Init) for ADC_Init
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd) for ADC_DMARequestAfterLastTransferCmd
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_DMACmd) for ADC_DMACmd
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_Cmd) for ADC_Cmd
    drive_dma.o(i.ADC1_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_SoftwareStartConv) for ADC_SoftwareStartConv
    drive_dma.o(i.ADC1_DMA2_Reload) refers to drive_dma.o(.bss) for .bss
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_dma.o(i.DMA_GetCmdStatus) for DMA_GetCmdStatus
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_dma.o(i.DMA_SetCurrDataCounter) for DMA_SetCurrDataCounter
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_adc.o(i.ADC_CommonInit) for ADC_CommonInit
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_adc.o(i.ADC_Init) for ADC_Init
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd) for ADC_DMARequestAfterLastTransferCmd
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_adc.o(i.ADC_DMACmd) for ADC_DMACmd
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_adc.o(i.ADC_Cmd) for ADC_Cmd
    drive_dma.o(i.ADC3_DMA2_Init) refers to stm32f4xx_adc.o(i.ADC_SoftwareStartConv) for ADC_SoftwareStartConv
    drive_dma.o(i.ADC3_DMA2_Init) refers to drive_dma.o(.bss) for .bss
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_GetCmdStatus) for DMA_GetCmdStatus
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_SetCurrDataCounter) for DMA_SetCurrDataCounter
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_CommonInit) for ADC_CommonInit
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_Init) for ADC_Init
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd) for ADC_DMARequestAfterLastTransferCmd
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_DMACmd) for ADC_DMACmd
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_Cmd) for ADC_Cmd
    drive_dma.o(i.ADC3_DMA2_Reload) refers to stm32f4xx_adc.o(i.ADC_SoftwareStartConv) for ADC_SoftwareStartConv
    drive_dma.o(i.ADC3_DMA2_Reload) refers to drive_dma.o(.bss) for .bss
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_dma.o(i.DMA_GetCmdStatus) for DMA_GetCmdStatus
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_dma.o(i.DMA_SetCurrDataCounter) for DMA_SetCurrDataCounter
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_dac.o(i.DAC_Init) for DAC_Init
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_dac.o(i.DAC_SetChannel1Data) for DAC_SetChannel1Data
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_dac.o(i.DAC_DMACmd) for DAC_DMACmd
    drive_dma.o(i.DAC1_DMA1_Init) refers to stm32f4xx_dac.o(i.DAC_Cmd) for DAC_Cmd
    drive_dma.o(i.DAC1_DMA1_Init) refers to drive_dma.o(i.DAC1_DMA1_Reload) for DAC1_DMA1_Reload
    drive_dma.o(i.DAC1_DMA1_Init) refers to drive_dma.o(.data) for .data
    drive_dma.o(i.DAC1_DMA1_Reload) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_dma.o(i.DAC1_DMA1_Reload) refers to stm32f4xx_tim.o(i.TIM_TimeBaseStructInit) for TIM_TimeBaseStructInit
    drive_dma.o(i.DAC1_DMA1_Reload) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_dma.o(i.DAC1_DMA1_Reload) refers to stm32f4xx_tim.o(i.TIM_SelectOutputTrigger) for TIM_SelectOutputTrigger
    drive_dma.o(i.DAC1_DMA1_Reload) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM10_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_timer.o(i.TIM10_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM10_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM10_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM10_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM11_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_timer.o(i.TIM11_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM11_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM11_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM11_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM12_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_timer.o(i.TIM12_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM12_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM12_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM12_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM13_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_timer.o(i.TIM13_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM13_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM13_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM13_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM14_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_timer.o(i.TIM14_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM14_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM14_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM14_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM1_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_timer.o(i.TIM1_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM1_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM1_Init) refers to stm32f4xx_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    drive_timer.o(i.TIM1_Init) refers to stm32f4xx_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    drive_timer.o(i.TIM1_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM1_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM2_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_timer.o(i.TIM2_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_timer.o(i.TIM2_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_timer.o(i.TIM2_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    drive_timer.o(i.TIM2_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM2_Init) refers to stm32f4xx_tim.o(i.TIM_ICInit) for TIM_ICInit
    drive_timer.o(i.TIM2_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM2_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM2_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM3_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_timer.o(i.TIM3_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM3_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseStructInit) for TIM_TimeBaseStructInit
    drive_timer.o(i.TIM3_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM3_Init) refers to stm32f4xx_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    drive_timer.o(i.TIM3_Init) refers to stm32f4xx_tim.o(i.TIM_SelectOutputTrigger) for TIM_SelectOutputTrigger
    drive_timer.o(i.TIM4_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_timer.o(i.TIM4_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM4_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM4_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM4_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM5_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_timer.o(i.TIM5_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_timer.o(i.TIM5_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_timer.o(i.TIM5_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    drive_timer.o(i.TIM5_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM5_Init) refers to stm32f4xx_tim.o(i.TIM_ICInit) for TIM_ICInit
    drive_timer.o(i.TIM5_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM5_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM5_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM6_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_timer.o(i.TIM6_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM6_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM6_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM6_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM7_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_timer.o(i.TIM7_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM7_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM7_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM7_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM8_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_timer.o(i.TIM8_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_timer.o(i.TIM8_Init) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    drive_timer.o(i.TIM8_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_timer.o(i.TIM8_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM8_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM8_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM8_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_timer.o(i.TIM9_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_timer.o(i.TIM9_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_timer.o(i.TIM9_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_timer.o(i.TIM9_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_timer.o(i.TIM9_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_touch.o(i.ADS_Read_AD) refers to drive_touch.o(i.ADS_Write_Byte) for ADS_Write_Byte
    drive_touch.o(i.ADS_Read_AD) refers to delay.o(i.delay_us) for delay_us
    drive_touch.o(i.ADS_Read_AD) refers to drive_touch.o(i.Delay) for Delay
    drive_touch.o(i.ADS_Read_XY) refers to drive_touch.o(i.ADS_Read_AD) for ADS_Read_AD
    drive_touch.o(i.ADS_Write_Byte) refers to drive_touch.o(i.Delay) for Delay
    drive_touch.o(i.GPIO_Configuration) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_touch.o(i.GPIO_Configuration) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_touch.o(i.TouchRead) refers to drive_touch.o(i.ADS_Read_XY) for ADS_Read_XY
    drive_touch.o(i.Touch_Init) refers to drive_touch.o(i.GPIO_Configuration) for GPIO_Configuration
    drive_touchkey.o(i.Clear_Show) refers to os_ui.o(i.OS_Rect_Draw) for OS_Rect_Draw
    drive_touchkey.o(i.Clear_Show) refers to drive_touchkey.o(.data) for .data
    drive_touchkey.o(i.Interface) refers to tft_lcd.o(i.LCD_Clear) for LCD_Clear
    drive_touchkey.o(i.Interface) refers to os_ui.o(i.OS_String_Show) for OS_String_Show
    drive_touchkey.o(i.Interface) refers to os_ui.o(i.OS_Rect_Draw) for OS_Rect_Draw
    drive_touchkey.o(i.Interface) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    drive_touchkey.o(i.Interface) refers to os_ui.o(i.OS_Num_Show) for OS_Num_Show
    drive_touchkey.o(i.TouchKey_Draw) refers to os_ui.o(i.OS_Rect_Draw) for OS_Rect_Draw
    drive_touchkey.o(i.TouchKey_Draw) refers to os_ui.o(i.OS_Line_Draw) for OS_Line_Draw
    drive_touchkey.o(i.TouchKey_Draw) refers to os_ui.o(i.OS_String_Show) for OS_String_Show
    drive_touchkey.o(i.TouchKey_Draw) refers to drive_touchkey.o(.data) for .data
    drive_touchkey.o(i.TouchKey_Scan) refers to app_touch.o(i.Touch_Judge) for Touch_Judge
    drive_touchkey.o(i.TouchKey_Scan) refers to drive_touchkey.o(i.Clear_Show) for Clear_Show
    drive_touchkey.o(i.TouchKey_Scan) refers to os_ui.o(i.OS_String_Show) for OS_String_Show
    drive_touchkey.o(i.TouchKey_Scan) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    drive_touchkey.o(i.TouchKey_Scan) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    drive_touchkey.o(i.TouchKey_Scan) refers to drive_touchkey.o(.data) for .data
    drive_touchkey.o(i.TouchKey_Scan) refers to drive_touchkey.o(.bss) for .bss
    drive_pwm.o(i.PWM1_CCR_Set) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    drive_pwm.o(i.PWM1_CCR_Set) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_tim.o(i.TIM_OC2Init) for TIM_OC2Init
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_tim.o(i.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_pwm.o(i.PWM1_Init) refers to stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    drive_pwm.o(i.PWM2_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_pwm.o(i.PWM2_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_pwm.o(i.PWM2_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_pwm.o(i.PWM2_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    drive_pwm.o(i.PWM2_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_pwm.o(i.PWM2_Init) refers to stm32f4xx_tim.o(i.TIM_ICInit) for TIM_ICInit
    drive_pwm.o(i.PWM2_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    drive_pwm.o(i.PWM2_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_pwm.o(i.PWM2_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    user_spi.o(i.SPI_GPIO_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user_spi.o(i.SPI_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    user_spi.o(i.SPI_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    user_spi.o(i.User_SPI_Init) refers to user_spi.o(i.SPI_GPIO_Init) for SPI_GPIO_Init
    user_spi.o(i.User_SPI_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    user_spi.o(i.User_SPI_Init) refers to stm32f4xx_spi.o(i.SPI_Init) for SPI_Init
    user_spi.o(i.User_SPI_SendData) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    user_spi.o(i.User_SPI_SendData) refers to delay.o(i.delay_us) for delay_us
    user_spi.o(i.User_SPI_SendData) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    user_bgd.o(i.Check_BGD) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    user_bgd.o(i.Check_BGD) refers to user_bgd.o(i.Grad_Descent) for Grad_Descent
    user_bgd.o(i.Check_BGD) refers to user_bgd.o(.constdata) for .constdata
    user_bgd.o(i.Grad_Descent) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    user_bgd.o(i.Grad_Descent) refers to user_bgd.o(i.GetSum) for GetSum
    user_bgd.o(i.Grad_Descent) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user_bgd.o(i.Grad_Descent) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    user_bgd.o(i.Grad_Descent) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    drive_ps2.o(i.Key_StateSweep) refers to drive_ps2.o(i.PS2_ReadKeyCodon) for PS2_ReadKeyCodon
    drive_ps2.o(i.MyPs2KeyScan) refers to drive_ps2.o(i.Key_StateSweep) for Key_StateSweep
    drive_ps2.o(i.MyPs2KeyScan) refers to delay.o(i.delay_ms) for delay_ms
    drive_ps2.o(i.MyPs2KeyScan) refers to drive_ps2.o(.bss) for .bss
    drive_ps2.o(i.MyPs2KeyScan) refers to drive_ps2.o(.data) for .data
    drive_ps2.o(i.PS2_GPIO_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_ps2.o(i.PS2_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_ps2.o(i.PS2_Keyboard_Init) refers to drive_ps2.o(i.PS2_GPIO_Init) for PS2_GPIO_Init
    drive_ps2.o(i.PS2_ReadKeyCodon) refers to drive_ps2.o(i.PS2_SCL_Set) for PS2_SCL_Set
    drive_ps2.o(i.PS2_ReadKeyCodon) refers to drive_ps2.o(i.PS2_SCL_Wait) for PS2_SCL_Wait
    drive_ps2.o(i.PS2_ReadKeyCodon) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    drive_ps2.o(i.PS2_SCL_Set) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_ps2.o(i.PS2_SCL_Set) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_ps2.o(i.PS2_SCL_Set) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_ps2.o(i.PS2_SCL_Set) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    drive_ps2.o(i.PS2_SCL_Wait) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    drive_ps2.o(i.PS2_SCL_Wait) refers to drive_ps2.o(i.PS2_SCL_Set) for PS2_SCL_Set
    drive_ps2.o(i.PS2_SCL_Wait) refers to drive_ps2.o(.data) for .data
    user_dac8562.o(i.DAC8562_GPIOInit) refers to user_spi.o(i.SPI_GPIO_Init) for SPI_GPIO_Init
    user_dac8562.o(i.DAC8562_GPIOInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user_dac8562.o(i.DAC8562_GPIOInit) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    user_dac8562.o(i.DAC8562_Init) refers to user_dac8562.o(i.DAC8562_GPIOInit) for DAC8562_GPIOInit
    user_dac8562.o(i.DAC8562_Init) refers to user_spi.o(i.User_SPI_SendData) for User_SPI_SendData
    user_dac8562.o(i.DAC8562_OutAC) refers to user_dac8562.o(.data) for .data
    user_dac8562.o(i.DAC8562_OutDC) refers to user_spi.o(i.User_SPI_SendData) for User_SPI_SendData
    user_dac8562.o(i.DAC8562_OutDC) refers to user_dac8562.o(.data) for .data
    user_dac8562.o(i.Set_ACData) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user_dac8562.o(i.Set_ACData) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    user_dac8562.o(i.Set_ACData) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    user_dac8562.o(i.Set_ACData) refers to user_dac8562.o(.data) for .data
    user_dac8562.o(i.Set_ACData) refers to user_dac8562.o(.bss) for .bss
    user_ad8370.o(i.AD8370_GPIOInit) refers to user_spi.o(i.SPI_GPIO_Init) for SPI_GPIO_Init
    user_ad8370.o(i.AD8370_GPIOInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user_ad8370.o(i.AD8370_GPIOInit) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    user_ad8370.o(i.AD8370_Init) refers to user_ad8370.o(i.AD8370_GPIOInit) for AD8370_GPIOInit
    user_ad8370.o(i.AD8370_Init) refers to user_ad8370.o(i.AD8370_SetTimes) for AD8370_SetTimes
    user_ad8370.o(i.AD8370_SetTimes) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user_ad8370.o(i.AD8370_SetTimes) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    user_ad8370.o(i.AD8370_SetTimes) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    user_ad8370.o(i.AD8370_SetTimes) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    user_ad8370.o(i.AD8370_SetTimes) refers to user_spi.o(i.User_SPI_SendData) for User_SPI_SendData
    user_ad8370.o(i.AD8370_SetTimes) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    user_iic.o(i.IIC_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user_iic.o(i.IIC_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    user_iic.o(i.IIC_Init) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    user_iic.o(i.IIC_SendData) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    user_iic.o(i.IIC_SendData) refers to delay.o(i.delay_us) for delay_us
    user_iic.o(i.IIC_SendData) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    user_iic.o(i.IIC_SendData) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    user_iic.o(i.IIC_Start) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    user_iic.o(i.IIC_Start) refers to delay.o(i.delay_us) for delay_us
    user_iic.o(i.IIC_Start) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    user_iic.o(i.IIC_Stop) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    user_iic.o(i.IIC_Stop) refers to delay.o(i.delay_us) for delay_us
    user_iic.o(i.IIC_Stop) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_communication.o(i.DDSDataInit) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    drive_communication.o(i.DDSDataInit) refers to drive_communication.o(i.sendData) for sendData
    drive_communication.o(i.DDSDataInit) refers to delay.o(i.delay_ms) for delay_ms
    drive_communication.o(i.DDSDataInit) refers to drive_communication.o(.bss) for .bss
    drive_communication.o(i.Init_Uart) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_communication.o(i.Init_Uart) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    drive_communication.o(i.Init_Uart) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    drive_communication.o(i.Init_Uart) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_communication.o(i.Init_Uart) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    drive_communication.o(i.Init_Uart) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    drive_communication.o(i.sendData) refers to drive_communication.o(i.crc_16) for crc_16
    drive_communication.o(i.sendData) refers to drive_communication.o(i.usartSendData) for usartSendData
    drive_communication.o(i.sendData) refers to drive_communication.o(.bss) for .bss
    drive_communication.o(i.usartSendData) refers to stm32f4xx_usart.o(i.USART_SendData) for USART_SendData
    drive_communication.o(i.usartSendData) refers to stm32f4xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    drive_ads1256.o(i.ADS1256RREG) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    drive_ads1256.o(i.ADS1256RREG) refers to drive_ads1256.o(i.SPI_WriteByte) for SPI_WriteByte
    drive_ads1256.o(i.ADS1256RREG) refers to delay.o(i.delay_us) for delay_us
    drive_ads1256.o(i.ADS1256RREG) refers to drive_ads1256.o(i.SPI_ReadByte) for SPI_ReadByte
    drive_ads1256.o(i.ADS1256RREG) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_ads1256.o(i.ADS1256ReadData) refers to drive_ads1256.o(i.ADS1256WREG) for ADS1256WREG
    drive_ads1256.o(i.ADS1256ReadData) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    drive_ads1256.o(i.ADS1256ReadData) refers to delay.o(i.delay_us) for delay_us
    drive_ads1256.o(i.ADS1256ReadData) refers to drive_ads1256.o(i.SPI_WriteByte) for SPI_WriteByte
    drive_ads1256.o(i.ADS1256ReadData) refers to drive_ads1256.o(i.SPI_ReadByte) for SPI_ReadByte
    drive_ads1256.o(i.ADS1256ReadData) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_ads1256.o(i.ADS1256WREG) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    drive_ads1256.o(i.ADS1256WREG) refers to drive_ads1256.o(i.SPI_WriteByte) for SPI_WriteByte
    drive_ads1256.o(i.ADS1256WREG) refers to delay.o(i.delay_us) for delay_us
    drive_ads1256.o(i.ADS1256WREG) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_ads1256.o(i.ADS1256_Init) refers to drive_ads1256.o(i.Init_ADS1256_GPIO) for Init_ADS1256_GPIO
    drive_ads1256.o(i.ADS1256_Init) refers to delay.o(i.delay_ms) for delay_ms
    drive_ads1256.o(i.ADS1256_Init) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    drive_ads1256.o(i.ADS1256_Init) refers to delay.o(i.delay_us) for delay_us
    drive_ads1256.o(i.ADS1256_Init) refers to drive_ads1256.o(i.SPI_WriteByte) for SPI_WriteByte
    drive_ads1256.o(i.ADS1256_Init) refers to drive_ads1256.o(i.ADS1256WREG) for ADS1256WREG
    drive_ads1256.o(i.ADS1256_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_ads1256.o(i.Init_ADS1256_GPIO) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_ads1256.o(i.Init_ADS1256_GPIO) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_ads1256.o(i.Init_ADS1256_GPIO) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_ads1256.o(i.Moving_Average_Filter) refers to drive_ads1256.o(i.ADS1256ReadData) for ADS1256ReadData
    drive_ads1256.o(i.SPI_ReadByte) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_ads1256.o(i.SPI_ReadByte) refers to delay.o(i.delay_us) for delay_us
    drive_ads1256.o(i.SPI_ReadByte) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    drive_ads1256.o(i.SPI_WriteByte) refers to delay.o(i.delay_us) for delay_us
    drive_ads1256.o(i.SPI_WriteByte) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    drive_ads1256.o(i.SPI_WriteByte) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    drive_fft.o(i.Wn_i) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    drive_fft.o(i.Wn_i) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    drive_fft.o(i.Wn_i) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    drive_fft.o(i.Wn_i) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    drive_fft.o(i.c_abs) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    drive_fft.o(i.c_abs) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    drive_fft.o(i.c_abs) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    drive_fft.o(i.complex_abs_float) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    drive_fft.o(i.complex_abs_float) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    drive_fft.o(i.complex_abs_float) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    drive_fft.o(i.fft) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    drive_fft.o(i.fft) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    drive_fft.o(i.fft) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    drive_fft.o(i.fft) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    drive_fft.o(i.fft) refers to drive_fft.o(i.Wn_i) for Wn_i
    drive_fft.o(i.fft) refers to drive_fft.o(i.c_mul) for c_mul
    drive_fft.o(i.fft) refers to drive_fft.o(i.c_sub) for c_sub
    drive_fft.o(i.fft) refers to drive_fft.o(i.c_plus) for c_plus
    drive_fft.o(i.fft_process) refers to arm_cfft_radix2_init_f32.o(.text) for arm_cfft_radix2_init_f32
    drive_fft.o(i.fft_process) refers to arm_cfft_radix2_f32.o(.text) for arm_cfft_radix2_f32
    drive_fft.o(i.fft_process) refers to drive_fft.o(.bss) for .bss
    drive_fft.o(i.ifft) refers to drive_fft.o(i.conjugate_complex) for conjugate_complex
    drive_fft.o(i.ifft) refers to drive_fft.o(i.fft) for fft
    user_pga2310.o(i.PGA2310_Init) refers to user_spi.o(i.SPI_GPIO_Init) for SPI_GPIO_Init
    user_pga2310.o(i.PGA2310_Init) refers to user_pga2310.o(i.PGA2310_SetAv) for PGA2310_SetAv
    user_pga2310.o(i.PGA2310_SetAv) refers to user_spi.o(i.User_SPI_SendData) for User_SPI_SendData
    user_dac.o(i.DAC1_Vol_Set) refers to user_dac.o(.data) for .data
    user_dac.o(i.Set_TriggerFre) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    user_dac.o(i.Set_WaveData) refers to user_dac.o(i.User_DAC_DMA_Init) for User_DAC_DMA_Init
    user_dac.o(i.Set_WaveData) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    user_dac.o(i.Set_WaveData) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    user_dac.o(i.Set_WaveData) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    user_dac.o(i.Set_WaveData) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    user_dac.o(i.Set_WaveData) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    user_dac.o(i.Set_WaveData) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    user_dac.o(i.Set_WaveData) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    user_dac.o(i.Set_WaveData) refers to user_dac.o(.bss) for .bss
    user_dac.o(i.Set_WaveData) refers to user_dac.o(.data) for .data
    user_dac.o(i.User_DAC_Configure) refers to stm32f4xx_dac.o(i.DAC_DeInit) for DAC_DeInit
    user_dac.o(i.User_DAC_Configure) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    user_dac.o(i.User_DAC_Configure) refers to stm32f4xx_dac.o(i.DAC_StructInit) for DAC_StructInit
    user_dac.o(i.User_DAC_Configure) refers to stm32f4xx_dac.o(i.DAC_Init) for DAC_Init
    user_dac.o(i.User_DAC_Configure) refers to stm32f4xx_dac.o(i.DAC_DMACmd) for DAC_DMACmd
    user_dac.o(i.User_DAC_Configure) refers to stm32f4xx_dac.o(i.DAC_Cmd) for DAC_Cmd
    user_dac.o(i.User_DAC_DMA_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user_dac.o(i.User_DAC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    user_dac.o(i.User_DAC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    user_dac.o(i.User_DAC_DMA_Init) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    user_dac.o(i.User_DAC_DMA_Init) refers to user_dac.o(.bss) for .bss
    user_dac.o(i.User_DAC_GPIO_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    user_dac.o(i.User_DAC_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    user_dac.o(i.User_DAC_Init) refers to user_dac.o(i.User_DAC_GPIO_Init) for User_DAC_GPIO_Init
    user_dac.o(i.User_DAC_Init) refers to user_dac.o(i.User_DAC_TIM_Init) for User_DAC_TIM_Init
    user_dac.o(i.User_DAC_Init) refers to user_dac.o(i.User_DAC_DMA_Init) for User_DAC_DMA_Init
    user_dac.o(i.User_DAC_Init) refers to user_dac.o(i.User_DAC_Configure) for User_DAC_Configure
    user_dac.o(i.User_DAC_Init) refers to stm32f4xx_dac.o(i.DAC_SetChannel1Data) for DAC_SetChannel1Data
    user_dac.o(i.User_DAC_TIM_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    user_dac.o(i.User_DAC_TIM_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    user_dac.o(i.User_DAC_TIM_Init) refers to stm32f4xx_tim.o(i.TIM_SelectOutputTrigger) for TIM_SelectOutputTrigger
    user_dac.o(i.User_DAC_TIM_Init) refers to stm32f4xx_tim.o(i.TIM_UpdateDisableConfig) for TIM_UpdateDisableConfig
    user_dac.o(i.User_DAC_TIM_Init) refers to stm32f4xx_tim.o(i.TIM_DMACmd) for TIM_DMACmd
    user_dac.o(i.User_DAC_TIM_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    user_dac.o(i.User_DAC_TIM_Init) refers to user_dac.o(i.Set_TriggerFre) for Set_TriggerFre
    user_dac.o(i.User_DAC_TIM_NVIC_Init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    user_dac.o(i.User_DAC_TIM_NVIC_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    drive_dac.o(i.DAC1_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_dac.o(i.DAC1_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_dac.o(i.DAC1_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_dac.o(i.DAC1_Init) refers to stm32f4xx_dac.o(i.DAC_Init) for DAC_Init
    drive_dac.o(i.DAC1_Init) refers to stm32f4xx_dac.o(i.DAC_Cmd) for DAC_Cmd
    drive_dac.o(i.DAC1_Init) refers to stm32f4xx_dac.o(i.DAC_SetChannel1Data) for DAC_SetChannel1Data
    drive_dac.o(i.dacClose) refers to stm32f4xx_dac.o(i.DAC_Cmd) for DAC_Cmd
    drive_dac.o(i.dacClose) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_dac.o(i.dacInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    drive_dac.o(i.dacInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_dac.o(i.dacInit) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    drive_dac.o(i.dacInit) refers to stm32f4xx_dac.o(i.DAC_Init) for DAC_Init
    drive_dac.o(i.dacInit) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    drive_dac.o(i.dacInit) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    drive_dac.o(i.dacInit) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    drive_dac.o(i.dacInit) refers to stm32f4xx_dac.o(i.DAC_DMACmd) for DAC_DMACmd
    drive_dac.o(i.dacInit) refers to stm32f4xx_dac.o(i.DAC_Cmd) for DAC_Cmd
    drive_dac.o(i.dacInit) refers to stm32f4xx_dac.o(i.DAC_SoftwareTriggerCmd) for DAC_SoftwareTriggerCmd
    drive_dac.o(i.dacInit) refers to drive_dac.o(i.ddsDataInit) for ddsDataInit
    drive_dac.o(i.dacInit) refers to drive_dac.o(.bss) for .bss
    drive_dac.o(i.dacInit) refers to drive_dac.o(.data) for .data
    drive_dac.o(i.dacOpen) refers to stm32f4xx_dac.o(i.DAC_Cmd) for DAC_Cmd
    drive_dac.o(i.dacOpen) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_dac.o(i.ddsDataInit) refers to drive_dac.o(.bss) for .bss
    drive_dac.o(i.ddsDataInit) refers to drive_dac.o(i.ddsSinWave) for ddsSinWave
    drive_dac.o(i.ddsSawtoothWave) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    drive_dac.o(i.ddsSawtoothWave) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    drive_dac.o(i.ddsSawtoothWave) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    drive_dac.o(i.ddsSawtoothWave) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    drive_dac.o(i.ddsSinWave) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    drive_dac.o(i.ddsSinWave) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    drive_dac.o(i.ddsSinWave) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    drive_dac.o(i.ddsSinWave) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    drive_dac.o(i.ddsSquareWave) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    drive_dac.o(i.ddsSquareWave) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    drive_dac.o(i.ddsSquareWave) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    drive_dac.o(i.ddsSquareWave) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    drive_dac.o(i.ddsTriangleWave) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    drive_dac.o(i.setDDS) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    drive_dac.o(i.setDDS) refers to drive_dac.o(i.dacInit) for dacInit
    drive_dac.o(i.setDDS) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    drive_dac.o(i.setDDS) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    drive_dac.o(i.setDDS) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    drive_dac.o(i.setDDS) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    drive_dac.o(i.setDDS) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    drive_dac.o(i.setDDS) refers to drive_dac.o(i.timer6Init) for timer6Init
    drive_dac.o(i.setDDS) refers to drive_dac.o(.bss) for .bss
    drive_dac.o(i.setDDS) refers to drive_dac.o(.data) for .data
    drive_dac.o(i.setDDS) refers to drive_dac.o(i.ddsSinWave) for ddsSinWave
    drive_dac.o(i.setDDS) refers to drive_dac.o(i.ddsTriangleWave) for ddsTriangleWave
    drive_dac.o(i.setDDS) refers to drive_dac.o(i.ddsSawtoothWave) for ddsSawtoothWave
    drive_dac.o(i.setDDS) refers to drive_dac.o(i.ddsSquareWave) for ddsSquareWave
    drive_dac.o(i.timer6Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    drive_dac.o(i.timer6Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    drive_dac.o(i.timer6Init) refers to stm32f4xx_tim.o(i.TIM_SelectOutputTrigger) for TIM_SelectOutputTrigger
    drive_dac.o(i.timer6Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    character.o(i.LCD_GB1616) refers to tft_lcd.o(i.PutPixel) for PutPixel
    character.o(i.LCD_GB1616) refers to character.o(.constdata) for .constdata
    character.o(i.LCD_GB3232) refers to tft_lcd.o(i.PutPixel) for PutPixel
    character.o(i.LCD_GB3232) refers to character.o(.constdata) for .constdata
    character.o(i.LCD_GB4848) refers to tft_lcd.o(i.PutPixel) for PutPixel
    character.o(i.LCD_GB4848) refers to character.o(.constdata) for .constdata
    character.o(i.Show_Str32) refers to character.o(i.LCD_GB3232) for LCD_GB3232
    character.o(i.Show_Str32) refers to tft_lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    character.o(i.Show_Str48) refers to character.o(i.LCD_GB4848) for LCD_GB4848
    character.o(i.Show_Str48) refers to tft_lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    fonts.o(.data) refers to fonts.o(.constdata) for Font_1608
    fonts.o(.data) refers to fonts.o(.constdata) for Font_2412
    fonts.o(.data) refers to fonts.o(.constdata) for Font_1608x
    fonts.o(.data) refers to fonts.o(.constdata) for Font_3216
    fontupd.o(i.font_init) refers to w25q64.o(i.W25Q64_Init) for W25Q64_Init
    fontupd.o(i.font_init) refers to w25q64.o(i.W25Q64_Read) for W25Q64_Read
    fontupd.o(i.font_init) refers to delay.o(i.delay_ms) for delay_ms
    fontupd.o(i.font_init) refers to fontupd.o(.bss) for .bss
    spi.o(i.SPI1_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    spi.o(i.SPI1_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    spi.o(i.SPI1_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    spi.o(i.SPI1_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    spi.o(i.SPI1_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    spi.o(i.SPI1_Init) refers to stm32f4xx_spi.o(i.SPI_Init) for SPI_Init
    spi.o(i.SPI1_Init) refers to stm32f4xx_spi.o(i.SPI_Cmd) for SPI_Cmd
    spi.o(i.SPI1_Init) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    spi.o(i.SPI1_ReadWriteByte) refers to stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus) for SPI_I2S_GetFlagStatus
    spi.o(i.SPI1_ReadWriteByte) refers to stm32f4xx_spi.o(i.SPI_I2S_SendData) for SPI_I2S_SendData
    spi.o(i.SPI1_ReadWriteByte) refers to stm32f4xx_spi.o(i.SPI_I2S_ReceiveData) for SPI_I2S_ReceiveData
    spi.o(i.SPI1_SetSpeed) refers to stm32f4xx_spi.o(i.SPI_Cmd) for SPI_Cmd
    text.o(i.Get_HzMat) refers to w25q64.o(i.W25Q64_Read) for W25Q64_Read
    text.o(i.Get_HzMat) refers to fontupd.o(.bss) for ftinfo
    text.o(i.Show_Font) refers to text.o(i.Get_HzMat) for Get_HzMat
    text.o(i.Show_Font) refers to tft_lcd.o(i.PutPixel) for PutPixel
    tft_lcd.o(i.Display_Control) refers to tft_lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    tft_lcd.o(i.Display_Control) refers to tft_lcd.o(.data) for .data
    tft_lcd.o(i.LCD_Appoint_Clear) refers to tft_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    tft_lcd.o(i.LCD_Clear) refers to tft_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    tft_lcd.o(i.LCD_CtrlLinesConfig) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    tft_lcd.o(i.LCD_CtrlLinesConfig) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    tft_lcd.o(i.LCD_CtrlLinesConfig) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    tft_lcd.o(i.LCD_Display0x) refers to tft_lcd.o(i.Display_Control) for Display_Control
    tft_lcd.o(i.LCD_DisplayNum) refers to tft_lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    tft_lcd.o(i.LCD_DisplayNum) refers to tft_lcd.o(.data) for .data
    tft_lcd.o(i.LCD_DisplayStringLine) refers to tft_lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    tft_lcd.o(i.LCD_DisplayStringLine) refers to tft_lcd.o(i.TFT_DispChar) for TFT_DispChar
    tft_lcd.o(i.LCD_DisplayStringLine) refers to tft_lcd.o(.data) for .data
    tft_lcd.o(i.LCD_Display_FloatNum) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    tft_lcd.o(i.LCD_Display_FloatNum) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    tft_lcd.o(i.LCD_Display_FloatNum) refers to tft_lcd.o(i.LCD_DisplayStringLine) for LCD_DisplayStringLine
    tft_lcd.o(i.LCD_Display_FloatNum) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    tft_lcd.o(i.LCD_Display_FloatNum) refers to dfixull.o(x$fpl$llufromd) for __aeabi_d2ulz
    tft_lcd.o(i.LCD_Display_FloatNum) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    tft_lcd.o(i.LCD_Display_FloatNum) refers to tft_lcd.o(i.LCD_GetFont) for LCD_GetFont
    tft_lcd.o(i.LCD_Display_FloatNum) refers to tft_lcd.o(i.LCD_DisplayNum) for LCD_DisplayNum
    tft_lcd.o(i.LCD_DrawCircle) refers to tft_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    tft_lcd.o(i.LCD_DrawCircle) refers to tft_lcd.o(.data) for .data
    tft_lcd.o(i.LCD_DrawCircleS) refers to tft_lcd.o(i.LCD_DrawCircle) for LCD_DrawCircle
    tft_lcd.o(i.LCD_DrawCircleS) refers to tft_lcd.o(.data) for .data
    tft_lcd.o(i.LCD_DrawLine) refers to tft_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    tft_lcd.o(i.LCD_DrawLine) refers to tft_lcd.o(.data) for .data
    tft_lcd.o(i.LCD_DrawRect) refers to tft_lcd.o(i.LCD_DrawLine) for LCD_DrawLine
    tft_lcd.o(i.LCD_DrawRectS) refers to tft_lcd.o(i.LCD_DrawuniLine) for LCD_DrawuniLine
    tft_lcd.o(i.LCD_DrawRectS) refers to tft_lcd.o(.data) for .data
    tft_lcd.o(i.LCD_DrawuniLine) refers to tft_lcd.o(i.PutPixel) for PutPixel
    tft_lcd.o(i.LCD_FSMCConfig) refers to stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd) for RCC_AHB3PeriphClockCmd
    tft_lcd.o(i.LCD_FSMCConfig) refers to stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit) for FSMC_NORSRAMInit
    tft_lcd.o(i.LCD_FSMCConfig) refers to stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd) for FSMC_NORSRAMCmd
    tft_lcd.o(i.LCD_GetColors) refers to tft_lcd.o(.data) for .data
    tft_lcd.o(i.LCD_GetFont) refers to tft_lcd.o(.data) for .data
    tft_lcd.o(i.LCD_SetBackColor) refers to tft_lcd.o(.data) for .data
    tft_lcd.o(i.LCD_SetColors) refers to tft_lcd.o(.data) for .data
    tft_lcd.o(i.LCD_SetFont) refers to tft_lcd.o(.data) for .data
    tft_lcd.o(i.LCD_SetTextColor) refers to tft_lcd.o(.data) for .data
    tft_lcd.o(i.LCD_ShowChar) refers to tft_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    tft_lcd.o(i.LCD_ShowChar) refers to tft_lcd.o(.data) for .data
    tft_lcd.o(i.PutPixel) refers to tft_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    tft_lcd.o(i.PutPixel) refers to tft_lcd.o(.data) for .data
    tft_lcd.o(i.TFT_DispChar) refers to tft_lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    tft_lcd.o(i.TFT_DispChar) refers to tft_lcd.o(.data) for .data
    tft_lcd.o(i.TFT_LCD_Init) refers to tft_lcd.o(i.LCD_CtrlLinesConfig) for LCD_CtrlLinesConfig
    tft_lcd.o(i.TFT_LCD_Init) refers to tft_lcd.o(i.LCD_FSMCConfig) for LCD_FSMCConfig
    tft_lcd.o(i.TFT_LCD_Init) refers to delay.o(i.delay_ms) for delay_ms
    tft_lcd.o(i.TFT_LCD_Init) refers to tft_lcd.o(i.LCD_SetDisplayWindow) for LCD_SetDisplayWindow
    tft_lcd.o(.data) refers to fonts.o(.data) for Font32x16
    w25q64.o(i.W25Q64_Erase_Chip) refers to w25q64.o(i.W25Q64_Write_Enable) for W25Q64_Write_Enable
    w25q64.o(i.W25Q64_Erase_Chip) refers to w25q64.o(i.W25Q64_Wait_Busy) for W25Q64_Wait_Busy
    w25q64.o(i.W25Q64_Erase_Chip) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_Erase_Chip) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_Erase_Chip) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_Erase_Sector) refers to w25q64.o(i.W25Q64_Write_Enable) for W25Q64_Write_Enable
    w25q64.o(i.W25Q64_Erase_Sector) refers to w25q64.o(i.W25Q64_Wait_Busy) for W25Q64_Wait_Busy
    w25q64.o(i.W25Q64_Erase_Sector) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_Erase_Sector) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_Erase_Sector) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    w25q64.o(i.W25Q64_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    w25q64.o(i.W25Q64_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_Init) refers to spi.o(i.SPI1_Init) for SPI1_Init
    w25q64.o(i.W25Q64_Init) refers to spi.o(i.SPI1_SetSpeed) for SPI1_SetSpeed
    w25q64.o(i.W25Q64_Init) refers to w25q64.o(i.W25Q64_ReadID) for W25Q64_ReadID
    w25q64.o(i.W25Q64_Init) refers to w25q64.o(.data) for .data
    w25q64.o(i.W25Q64_PowerDown) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_PowerDown) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_PowerDown) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_PowerDown) refers to delay.o(i.delay_us) for delay_us
    w25q64.o(i.W25Q64_Read) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_Read) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_Read) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_ReadID) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_ReadID) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_ReadID) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_ReadSR) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_ReadSR) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_ReadSR) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_WAKEUP) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_WAKEUP) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_WAKEUP) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_WAKEUP) refers to delay.o(i.delay_us) for delay_us
    w25q64.o(i.W25Q64_Wait_Busy) refers to w25q64.o(i.W25Q64_ReadSR) for W25Q64_ReadSR
    w25q64.o(i.W25Q64_Write) refers to w25q64.o(i.W25Q64_Erase_Sector) for W25Q64_Erase_Sector
    w25q64.o(i.W25Q64_Write) refers to w25q64.o(i.W25Q64_Write_NoCheck) for W25Q64_Write_NoCheck
    w25q64.o(i.W25Q64_Write) refers to w25q64.o(i.W25Q64_Read) for W25Q64_Read
    w25q64.o(i.W25Q64_Write) refers to w25q64.o(.bss) for .bss
    w25q64.o(i.W25Q64_Write_Disable) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_Write_Disable) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_Write_Disable) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_Write_Enable) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_Write_Enable) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_Write_Enable) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_Write_NoCheck) refers to w25q64.o(i.W25Q64_Write_Page) for W25Q64_Write_Page
    w25q64.o(i.W25Q64_Write_Page) refers to w25q64.o(i.W25Q64_Write_Enable) for W25Q64_Write_Enable
    w25q64.o(i.W25Q64_Write_Page) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_Write_Page) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_Write_Page) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    w25q64.o(i.W25Q64_Write_Page) refers to w25q64.o(i.W25Q64_Wait_Busy) for W25Q64_Wait_Busy
    w25q64.o(i.W25Q64_Write_SR) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    w25q64.o(i.W25Q64_Write_SR) refers to spi.o(i.SPI1_ReadWriteByte) for SPI1_ReadWriteByte
    w25q64.o(i.W25Q64_Write_SR) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    stm32f4xx_adc.o(i.ADC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_dac.o(i.DAC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllSectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseSector) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_Launch) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_RDPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramByte) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_flash.o(i.FLASH_GetStatus) for FLASH_GetStatus
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMStructInit) refers to stm32f4xx_fsmc.o(.constdata) for .constdata
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_i2c.o(i.I2C_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_i2c.o(i.I2C_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_rcc.o(i.RCC_GetClocksFreq) refers to stm32f4xx_rcc.o(.data) for .data
    stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI4_Config) for TI4_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI3_Config) for TI3_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_usart.o(i.USART_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_syscfg.o(i.SYSCFG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_can.o(i.CAN_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_can.o(i.CAN_GetITStatus) refers to stm32f4xx_can.o(i.CheckITStatus) for CheckITStatus
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemInit) refers to system_stm32f4xx.o(i.SetSysClock) for SetSysClock
    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f40_41xxx.o(RESET) refers to core.o(CODE) for PendSV_Handler
    startup_stm32f40_41xxx.o(RESET) refers to os_cpu.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to user_adc.o(i.DMA2_Stream0_IRQHandler) for DMA2_Stream0_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    arm_cfft_radix2_f32.o(.text) refers to arm_bitreversal.o(.text) for arm_bitreversal_f32
    arm_cfft_radix2_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_4096
    arm_cfft_radix2_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for armBitRevTable
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixull.o(x$fpl$llufromd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixull.o(x$fpl$llufromd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixull.o(x$fpl$llufromdr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixull.o(x$fpl$llufromdr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    atan2.o(i.__hardfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2.o(i.__hardfp_atan2) refers to _rserrno.o(.text) for __set_errno
    atan2.o(i.__hardfp_atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2_x.o(i.____hardfp_atan2$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atof.o(i.__hardfp_atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.__hardfp_atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.__hardfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__softfp_atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.__softfp_atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    cos.o(i.__hardfp_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.__hardfp_cos) refers to _rserrno.o(.text) for __set_errno
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos.o(i.__hardfp_cos) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos.o(i.__hardfp_cos) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos.o(i.__hardfp_cos) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos.o(i.__hardfp_cos) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    cos.o(i.__softfp_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.__softfp_cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos.o(i.cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos_x.o(i.____hardfp_cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.____hardfp_cos$lsc) refers to _rserrno.o(.text) for __set_errno
    cos_x.o(i.____hardfp_cos$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos_x.o(i.____hardfp_cos$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos_x.o(i.____hardfp_cos$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos_x.o(i.____hardfp_cos$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos_x.o(i.____hardfp_cos$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    cos_x.o(i.____softfp_cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.____softfp_cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    cos_x.o(i.__cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.__cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    pow.o(i.__hardfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to _rserrno.o(.text) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.____hardfp_pow$lsc) refers to _rserrno.o(.text) for __set_errno
    pow_x.o(i.____hardfp_pow$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    pow_x.o(i.____hardfp_pow$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow_x.o(i.____hardfp_pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.____hardfp_pow$lsc) refers to fabs.o(i.fabs) for fabs
    pow_x.o(i.____hardfp_pow$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow_x.o(i.____hardfp_pow$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow_x.o(i.____hardfp_pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow_x.o(i.____hardfp_pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.____hardfp_pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.____hardfp_pow$lsc) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow_x.o(i.____hardfp_pow$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__hardfp_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__hardfp_sin) refers to _rserrno.o(.text) for __set_errno
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin.o(i.__hardfp_sin) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin.o(i.__hardfp_sin) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.__hardfp_sin) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    sin.o(i.__hardfp_sin) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin.o(i.__softfp_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__softfp_sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin.o(i.sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin_x.o(i.____hardfp_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.____hardfp_sin$lsc) refers to _rserrno.o(.text) for __set_errno
    sin_x.o(i.____hardfp_sin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin_x.o(i.____hardfp_sin$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin_x.o(i.____hardfp_sin$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin_x.o(i.____hardfp_sin$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    sin_x.o(i.____hardfp_sin$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin_x.o(i.____softfp_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.____softfp_sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sin_x.o(i.__sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.__sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sinf.o(i.__hardfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to _rserrno.o(.text) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf_x.o(i.____hardfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____hardfp_sinf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf_x.o(i.____hardfp_sinf$lsc) refers to _rserrno.o(.text) for __set_errno
    sinf_x.o(i.____hardfp_sinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf_x.o(i.____softfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____softfp_sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sinf_x.o(i.__sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.__sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sqrt.o(i.__hardfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    strtod.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    strtod.o(.text) refers to scanf1.o(x$fpl$scanf1) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace.o(.text) for isspace
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    dsqrt_umaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    atan.o(i.__hardfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.__hardfp_atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.__hardfp_atan) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.____hardfp_atan$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    rred.o(i.__ieee754_rem_pio2) refers to fabs.o(i.fabs) for fabs
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    rred.o(i.__ieee754_rem_pio2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf1.o(x$fpl$scanf1) refers to scanf_fp.o(.text) for _scanf_really_real
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    scanf_fp.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf_fp.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    scanf_fp.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    scanf_fp.o(.text) refers to istatus.o(x$fpl$ieeestatus) for __ieee_status
    scanf_fp.o(.text) refers to bigflt0.o(.text) for _btod_etento
    scanf_fp.o(.text) refers to btod.o(CL$$btod_emuld) for _btod_emuld
    scanf_fp.o(.text) refers to btod.o(CL$$btod_edivd) for _btod_edivd
    scanf_fp.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    scanf_fp.o(.text) refers to scanf2.o(x$fpl$scanf2) for _scanf_infnan
    scanf_fp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to fpconst.o(c$$dmax) for __dbl_max
    scanf_fp.o(.text) refers to fpconst.o(c$$dinf) for __huge_val
    scanf_fp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    fpconst.o(c$$dinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$finf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dmax) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf2.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    scanf2b.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2b.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__hardfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to frexp.o(i.frexp) for frexp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    narrow.o(i.__hardfp___mathlib_tofloat) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    narrow.o(i.__hardfp___mathlib_tofloat) refers to _rserrno.o(.text) for __set_errno
    narrow.o(i.__hardfp___mathlib_tofloat) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    narrow.o(i.__mathlib_narrow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_narrow) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__softfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__softfp___mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    scanf_hexfp.o(.text) refers to _chval.o(.text) for _chval
    scanf_hexfp.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    scanf_hexfp.o(.text) refers to ldexp.o(i.__support_ldexp) for __support_ldexp
    scanf_hexfp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    frexp.o(i.__hardfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__hardfp_frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    frexp.o(i.__softfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__softfp_frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    frexp.o(i.frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    ldexp.o(i.__hardfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__hardfp_ldexp) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp.o(i.__hardfp_ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.__hardfp_ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp.o(i.__softfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__softfp_ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp.o(i.__support_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__support_ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp.o(i.ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to _rserrno.o(.text) for __set_errno
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    ldexp_x.o(i.____softfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____softfp_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.____support_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____support_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.__ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.__ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    os_cpu.o(i.OSSetPrioRdy) refers to os_cpu.o(.data) for .data
    os_cpu.o(i.OSDelPrioRdy) refers to os_cpu.o(.data) for .data


==============================================================================

Removing Unused input sections from the image.

    Removing app_touch.o(.rev16_text), (4 bytes).
    Removing app_touch.o(.revsh_text), (4 bytes).
    Removing app_touch.o(i.TouchStart_Judge), (44 bytes).
    Removing app_touch.o(i.Touch_Clear), (20 bytes).
    Removing app_touch.o(i.Touch_Judge), (68 bytes).
    Removing app_touch.o(i.Touch_main), (84 bytes).
    Removing app_touch.o(.bss), (14 bytes).
    Removing app_touch.o(.data), (4 bytes).
    Removing app_led.o(.rev16_text), (4 bytes).
    Removing app_led.o(.revsh_text), (4 bytes).
    Removing user_adc.o(.rev16_text), (4 bytes).
    Removing user_adc.o(.revsh_text), (4 bytes).
    Removing user_adc.o(i.ADC_DMA_Init), (140 bytes).
    Removing user_adc.o(i.ADC_DMA_NVIC_Init), (40 bytes).
    Removing user_adc.o(i.ADC_DualMode_RegSimult_Init), (176 bytes).
    Removing user_adc.o(i.ADC_Mode_Independent_Init), (120 bytes).
    Removing user_adc.o(i.ADC_TIM3_Init), (100 bytes).
    Removing user_adc.o(i.Get_ACVol), (132 bytes).
    Removing user_adc.o(i.Get_DCVol), (188 bytes).
    Removing user_adc.o(i.Set_SamplingFre), (104 bytes).
    Removing user_adc.o(i.User_ADC_GPIO_Init), (60 bytes).
    Removing user_adc.o(i.User_ADC_Init), (100 bytes).
    Removing user_adc.o(.bss), (4096 bytes).
    Removing user.o(.rev16_text), (4 bytes).
    Removing user.o(.revsh_text), (4 bytes).
    Removing user.o(i.AD9958_Vcc), (276 bytes).
    Removing user.o(i.ADS1256_ReadVol), (92 bytes).
    Removing user.o(i.Get_FreSpectrum), (320 bytes).
    Removing user.o(i.User_Abs), (16 bytes).
    Removing user.o(i.User_FixPhase), (52 bytes).
    Removing user.o(i.User_GetSignalInf), (332 bytes).
    Removing user.o(i.linear), (26 bytes).
    Removing user.o(i.meanFilter), (264 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing os_cpu.o(.rev16_text), (4 bytes).
    Removing os_cpu.o(.revsh_text), (4 bytes).
    Removing os_cpu.o(i.OSTaskRecovery), (96 bytes).
    Removing os_cpu.o(i.OSTaskSuspend), (96 bytes).
    Removing os_ui.o(.rev16_text), (4 bytes).
    Removing os_ui.o(.revsh_text), (4 bytes).
    Removing os_ui.o(i.OS_BackColor_Set), (16 bytes).
    Removing os_ui.o(i.OS_Circle_Draw), (68 bytes).
    Removing os_ui.o(i.OS_LCD_Clear), (10 bytes).
    Removing os_ui.o(i.OS_Picture_Draw), (1048 bytes).
    Removing os_ui.o(i.OS_TextColor_Set), (16 bytes).
    Removing os_ui.o(i.OS_Wave_Draw), (22 bytes).
    Removing os_ui.o(i.OS_Wave_Line_Show), (1758 bytes).
    Removing os_ui.o(i.OS_Wave_Unite), (40 bytes).
    Removing os_ui.o(i.OS_Wave_Windows_Show), (500 bytes).
    Removing os_ui.o(.data), (1 bytes).
    Removing os_malloc.o(.rev16_text), (4 bytes).
    Removing os_malloc.o(.revsh_text), (4 bytes).
    Removing os_malloc.o(i.my_mem_free), (92 bytes).
    Removing os_malloc.o(i.my_mem_init), (60 bytes).
    Removing os_malloc.o(i.my_mem_malloc), (156 bytes).
    Removing os_malloc.o(i.my_mem_perused), (56 bytes).
    Removing os_malloc.o(i.myfree), (48 bytes).
    Removing os_malloc.o(i.mymalloc), (52 bytes).
    Removing os_malloc.o(i.mymemcpy), (18 bytes).
    Removing os_malloc.o(i.mymemset), (14 bytes).
    Removing os_malloc.o(i.myrealloc), (80 bytes).
    Removing os_malloc.o(.bss), (32768 bytes).
    Removing os_malloc.o(.bss), (2048 bytes).
    Removing os_malloc.o(.constdata), (36 bytes).
    Removing os_malloc.o(.data), (36 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (6 bytes).
    Removing sys.o(i.INTX_DISABLE), (4 bytes).
    Removing sys.o(i.INTX_ENABLE), (4 bytes).
    Removing sys.o(i.WFI_SET), (4 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(i.fputc), (20 bytes).
    Removing usart.o(i.uart_init), (172 bytes).
    Removing usart.o(.data), (4 bytes).
    Removing drive_gpio.o(.rev16_text), (4 bytes).
    Removing drive_gpio.o(.revsh_text), (4 bytes).
    Removing drive_gpio.o(i.GPIO_Data_Init), (44 bytes).
    Removing drive_gpio.o(i.GPIO_Key_Init), (60 bytes).
    Removing drive_gpio.o(i.GPIO_POW_Init), (60 bytes).
    Removing drive_dma.o(.rev16_text), (4 bytes).
    Removing drive_dma.o(.revsh_text), (4 bytes).
    Removing drive_dma.o(i.ADC1_DMA2_Reload), (328 bytes).
    Removing drive_dma.o(i.ADC3_DMA2_Init), (284 bytes).
    Removing drive_dma.o(i.ADC3_DMA2_Reload), (292 bytes).
    Removing drive_dma.o(i.DAC1_DMA1_Init), (220 bytes).
    Removing drive_dma.o(i.DAC1_DMA1_Reload), (96 bytes).
    Removing drive_dma.o(.bss), (16096 bytes).
    Removing drive_dma.o(.data), (2 bytes).
    Removing drive_timer.o(.rev16_text), (4 bytes).
    Removing drive_timer.o(.revsh_text), (4 bytes).
    Removing drive_timer.o(i.TIM10_Init), (96 bytes).
    Removing drive_timer.o(i.TIM11_Init), (96 bytes).
    Removing drive_timer.o(i.TIM12_Init), (96 bytes).
    Removing drive_timer.o(i.TIM13_Init), (96 bytes).
    Removing drive_timer.o(i.TIM14_Init), (96 bytes).
    Removing drive_timer.o(i.TIM1_Init), (116 bytes).
    Removing drive_timer.o(i.TIM2_Init), (180 bytes).
    Removing drive_timer.o(i.TIM3_Init), (88 bytes).
    Removing drive_timer.o(i.TIM4_Init), (92 bytes).
    Removing drive_timer.o(i.TIM5_Init), (184 bytes).
    Removing drive_timer.o(i.TIM6_Init), (96 bytes).
    Removing drive_timer.o(i.TIM7_Init), (96 bytes).
    Removing drive_timer.o(i.TIM8_Init), (152 bytes).
    Removing drive_timer.o(i.TIM9_Init), (96 bytes).
    Removing drive_touch.o(.rev16_text), (4 bytes).
    Removing drive_touch.o(.revsh_text), (4 bytes).
    Removing drive_touch.o(i.ADS_Read_AD), (136 bytes).
    Removing drive_touch.o(i.ADS_Read_XY), (102 bytes).
    Removing drive_touch.o(i.ADS_Write_Byte), (76 bytes).
    Removing drive_touch.o(i.Delay), (12 bytes).
    Removing drive_touch.o(i.GPIO_Configuration), (148 bytes).
    Removing drive_touch.o(i.TouchRead), (142 bytes).
    Removing drive_touch.o(i.Touch_Init), (4 bytes).
    Removing drive_touchkey.o(.rev16_text), (4 bytes).
    Removing drive_touchkey.o(.revsh_text), (4 bytes).
    Removing drive_touchkey.o(i.Clear_Show), (56 bytes).
    Removing drive_touchkey.o(i.Interface), (760 bytes).
    Removing drive_touchkey.o(i.TouchKey_Draw), (440 bytes).
    Removing drive_touchkey.o(i.TouchKey_Scan), (852 bytes).
    Removing drive_touchkey.o(.bss), (40 bytes).
    Removing drive_touchkey.o(.data), (12 bytes).
    Removing drive_pwm.o(.rev16_text), (4 bytes).
    Removing drive_pwm.o(.revsh_text), (4 bytes).
    Removing drive_pwm.o(i.PWM1_CCR_Set), (52 bytes).
    Removing drive_pwm.o(i.PWM1_Init), (232 bytes).
    Removing drive_pwm.o(i.PWM2_Init), (184 bytes).
    Removing user_spi.o(.rev16_text), (4 bytes).
    Removing user_spi.o(.revsh_text), (4 bytes).
    Removing user_spi.o(i.SPI_GPIO_Init), (144 bytes).
    Removing user_spi.o(i.User_SPI_Init), (92 bytes).
    Removing user_spi.o(i.User_SPI_SendData), (176 bytes).
    Removing user_bgd.o(.rev16_text), (4 bytes).
    Removing user_bgd.o(.revsh_text), (4 bytes).
    Removing user_bgd.o(i.Check_BGD), (236 bytes).
    Removing user_bgd.o(i.GetSum), (32 bytes).
    Removing user_bgd.o(i.Grad_Descent), (572 bytes).
    Removing user_bgd.o(.constdata), (280 bytes).
    Removing drive_ps2.o(.rev16_text), (4 bytes).
    Removing drive_ps2.o(.revsh_text), (4 bytes).
    Removing user_dac8562.o(.rev16_text), (4 bytes).
    Removing user_dac8562.o(.revsh_text), (4 bytes).
    Removing user_dac8562.o(i.DAC8562_GPIOInit), (56 bytes).
    Removing user_dac8562.o(i.DAC8562_Init), (104 bytes).
    Removing user_dac8562.o(i.DAC8562_OutAC), (20 bytes).
    Removing user_dac8562.o(i.DAC8562_OutDC), (76 bytes).
    Removing user_dac8562.o(i.Set_ACData), (252 bytes).
    Removing user_dac8562.o(.bss), (128 bytes).
    Removing user_dac8562.o(.data), (8 bytes).
    Removing user_dac8562.o(.data), (1 bytes).
    Removing user_ad8370.o(.rev16_text), (4 bytes).
    Removing user_ad8370.o(.revsh_text), (4 bytes).
    Removing user_ad8370.o(i.AD8370_GPIOInit), (56 bytes).
    Removing user_ad8370.o(i.AD8370_Init), (20 bytes).
    Removing user_ad8370.o(i.AD8370_SetTimes), (228 bytes).
    Removing user_iic.o(.rev16_text), (4 bytes).
    Removing user_iic.o(.revsh_text), (4 bytes).
    Removing user_iic.o(i.IIC_Init), (68 bytes).
    Removing user_iic.o(i.IIC_RecData), (2 bytes).
    Removing user_iic.o(i.IIC_SendData), (128 bytes).
    Removing user_iic.o(i.IIC_Start), (56 bytes).
    Removing user_iic.o(i.IIC_Stop), (68 bytes).
    Removing drive_communication.o(.rev16_text), (4 bytes).
    Removing drive_communication.o(.revsh_text), (4 bytes).
    Removing drive_ads1256.o(.rev16_text), (4 bytes).
    Removing drive_ads1256.o(.revsh_text), (4 bytes).
    Removing drive_ads1256.o(i.ADS1256RREG), (76 bytes).
    Removing drive_fft.o(.rev16_text), (4 bytes).
    Removing drive_fft.o(.revsh_text), (4 bytes).
    Removing drive_fft.o(i.FFT_Harmonic), (152 bytes).
    Removing drive_fft.o(i.Wn_i), (212 bytes).
    Removing drive_fft.o(i.c_abs), (72 bytes).
    Removing drive_fft.o(i.c_div), (66 bytes).
    Removing drive_fft.o(i.c_mul), (50 bytes).
    Removing drive_fft.o(i.c_plus), (42 bytes).
    Removing drive_fft.o(i.c_sub), (42 bytes).
    Removing drive_fft.o(i.complex_abs_float), (52 bytes).
    Removing drive_fft.o(i.conjugate_complex), (42 bytes).
    Removing drive_fft.o(i.fft), (348 bytes).
    Removing drive_fft.o(i.fft_process), (128 bytes).
    Removing drive_fft.o(i.ifft), (90 bytes).
    Removing drive_fft.o(.bss), (20 bytes).
    Removing user_pga2310.o(.rev16_text), (4 bytes).
    Removing user_pga2310.o(.revsh_text), (4 bytes).
    Removing user_pga2310.o(i.PGA2310_Init), (28 bytes).
    Removing user_pga2310.o(i.PGA2310_SetAv), (120 bytes).
    Removing user_dac.o(.rev16_text), (4 bytes).
    Removing user_dac.o(.revsh_text), (4 bytes).
    Removing user_dac.o(i.DAC1_Vol_Set), (132 bytes).
    Removing user_dac.o(i.Set_TriggerFre), (100 bytes).
    Removing user_dac.o(i.Set_WaveData), (360 bytes).
    Removing user_dac.o(i.User_DAC_Configure), (46 bytes).
    Removing user_dac.o(i.User_DAC_DMA_Init), (108 bytes).
    Removing user_dac.o(i.User_DAC_GPIO_Init), (60 bytes).
    Removing user_dac.o(i.User_DAC_Init), (36 bytes).
    Removing user_dac.o(i.User_DAC_TIM_Init), (100 bytes).
    Removing user_dac.o(i.User_DAC_TIM_NVIC_Init), (40 bytes).
    Removing user_dac.o(.bss), (256 bytes).
    Removing user_dac.o(.data), (2 bytes).
    Removing user_dac.o(.data), (4 bytes).
    Removing drive_dac.o(.rev16_text), (4 bytes).
    Removing drive_dac.o(.revsh_text), (4 bytes).
    Removing drive_dac.o(i.DAC1_Init), (88 bytes).
    Removing drive_dac.o(i.dacClose), (28 bytes).
    Removing drive_dac.o(i.dacInit), (212 bytes).
    Removing drive_dac.o(i.dacOpen), (28 bytes).
    Removing drive_dac.o(i.ddsDataInit), (64 bytes).
    Removing drive_dac.o(i.ddsSawtoothWave), (124 bytes).
    Removing drive_dac.o(i.ddsSinWave), (76 bytes).
    Removing drive_dac.o(i.ddsSquareWave), (112 bytes).
    Removing drive_dac.o(i.ddsTriangleWave), (156 bytes).
    Removing drive_dac.o(i.setDDS), (464 bytes).
    Removing drive_dac.o(i.timer6Init), (72 bytes).
    Removing drive_dac.o(.bss), (2020 bytes).
    Removing drive_dac.o(.constdata), (8 bytes).
    Removing drive_dac.o(.data), (4 bytes).
    Removing character.o(.rev16_text), (4 bytes).
    Removing character.o(.revsh_text), (4 bytes).
    Removing character.o(i.LCD_GB1616), (168 bytes).
    Removing character.o(i.LCD_GB3232), (172 bytes).
    Removing character.o(i.LCD_GB4848), (172 bytes).
    Removing character.o(i.Show_Str32), (94 bytes).
    Removing character.o(i.Show_Str48), (94 bytes).
    Removing character.o(.constdata), (6242 bytes).
    Removing fonts.o(.constdata), (6080 bytes).
    Removing fonts.o(.constdata), (6080 bytes).
    Removing fonts.o(.constdata), (12480 bytes).
    Removing fonts.o(.data), (8 bytes).
    Removing fonts.o(.data), (8 bytes).
    Removing fonts.o(.data), (8 bytes).
    Removing fontupd.o(.rev16_text), (4 bytes).
    Removing fontupd.o(.revsh_text), (4 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing text.o(.rev16_text), (4 bytes).
    Removing text.o(.revsh_text), (4 bytes).
    Removing text.o(i.Get_HzMat), (156 bytes).
    Removing text.o(i.Show_Font), (158 bytes).
    Removing tft_lcd.o(.rev16_text), (4 bytes).
    Removing tft_lcd.o(.revsh_text), (4 bytes).
    Removing tft_lcd.o(i.Display_Control), (56 bytes).
    Removing tft_lcd.o(i.LCD_Display0x), (88 bytes).
    Removing tft_lcd.o(i.LCD_DisplayNum), (96 bytes).
    Removing tft_lcd.o(i.LCD_DisplayOff), (12 bytes).
    Removing tft_lcd.o(i.LCD_DisplayOn), (12 bytes).
    Removing tft_lcd.o(i.LCD_DisplayStringLine), (208 bytes).
    Removing tft_lcd.o(i.LCD_Display_FloatNum), (268 bytes).
    Removing tft_lcd.o(i.LCD_DrawCircle), (272 bytes).
    Removing tft_lcd.o(i.LCD_DrawCircleS), (48 bytes).
    Removing tft_lcd.o(i.LCD_DrawLine), (92 bytes).
    Removing tft_lcd.o(i.LCD_DrawPoint), (54 bytes).
    Removing tft_lcd.o(i.LCD_DrawPoint_4), (106 bytes).
    Removing tft_lcd.o(i.LCD_DrawRect), (66 bytes).
    Removing tft_lcd.o(i.LCD_GetColors), (16 bytes).
    Removing tft_lcd.o(i.LCD_GetFont), (12 bytes).
    Removing tft_lcd.o(i.LCD_ReadReg), (10 bytes).
    Removing tft_lcd.o(i.LCD_SetBackColor), (16 bytes).
    Removing tft_lcd.o(i.LCD_SetColors), (24 bytes).
    Removing tft_lcd.o(i.LCD_SetFont), (12 bytes).
    Removing tft_lcd.o(i.LCD_SetTextColor), (16 bytes).
    Removing tft_lcd.o(i.LCD_ShowChar), (272 bytes).
    Removing tft_lcd.o(i.LCD_WeBMP_SIZE), (26 bytes).
    Removing tft_lcd.o(i.LCD_WriteReg), (10 bytes).
    Removing tft_lcd.o(i.TFT_DispChar), (460 bytes).
    Removing w25q64.o(.rev16_text), (4 bytes).
    Removing w25q64.o(.revsh_text), (4 bytes).
    Removing w25q64.o(i.W25Q64_Erase_Chip), (52 bytes).
    Removing w25q64.o(i.W25Q64_PowerDown), (44 bytes).
    Removing w25q64.o(i.W25Q64_WAKEUP), (44 bytes).
    Removing w25q64.o(i.W25Q64_Write_Disable), (40 bytes).
    Removing w25q64.o(i.W25Q64_Write_SR), (48 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogCmd), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AutoInjectedConvCmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearITPendingBit), (8 bytes).
    Removing stm32f4xx_adc.o(i.ADC_Cmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_CommonInit), (40 bytes).
    Removing stm32f4xx_adc.o(i.ADC_CommonStructInit), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ContinuousModeCmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DMACmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DeInit), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeCmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_EOCOnEachRegularChannelCmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvEdgeConfig), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetConversionValue), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetFlagStatus), (14 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetITStatus), (30 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetInjectedConversionValue), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetMultiModeConversionValue), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartConvStatus), (14 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (14 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_Init), (76 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedChannelConfig), (74 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedDiscModeCmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedSequencerLengthConfig), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_MultiModeDMARequestAfterLastTransferCmd), (40 bytes).
    Removing stm32f4xx_adc.o(i.ADC_RegularChannelConfig), (116 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SetInjectedOffset), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartConv), (10 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartInjectedConv), (10 bytes).
    Removing stm32f4xx_adc.o(i.ADC_StructInit), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_TempSensorVrefintCmd), (40 bytes).
    Removing stm32f4xx_adc.o(i.ADC_VBATCmd), (40 bytes).
    Removing stm32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_crc.o(i.CRC_CalcBlockCRC), (28 bytes).
    Removing stm32f4xx_crc.o(i.CRC_CalcCRC), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearFlag), (12 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_dac.o(i.DAC_Cmd), (32 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DMACmd), (32 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DeInit), (24 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DualSoftwareTriggerCmd), (32 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetDataOutputValue), (24 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetFlagStatus), (24 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetITStatus), (36 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ITConfig), (28 bytes).
    Removing stm32f4xx_dac.o(i.DAC_Init), (40 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetChannel1Data), (20 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetChannel2Data), (20 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetDualChannelData), (28 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SoftwareTriggerCmd), (32 bytes).
    Removing stm32f4xx_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f4xx_dac.o(i.DAC_WaveGenerationCmd), (28 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ClearITPendingBit), (44 bytes).
    Removing stm32f4xx_dma.o(i.DMA_Cmd), (24 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DeInit), (300 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeCmd), (24 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeConfig), (26 bytes).
    Removing stm32f4xx_dma.o(i.DMA_FlowControllerConfig), (24 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCmdStatus), (14 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrDataCounter), (6 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrentMemoryTarget), (14 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFIFOStatus), (8 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFlagStatus), (52 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetITStatus), (84 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ITConfig), (50 bytes).
    Removing stm32f4xx_dma.o(i.DMA_Init), (84 bytes).
    Removing stm32f4xx_dma.o(i.DMA_MemoryTargetConfig), (12 bytes).
    Removing stm32f4xx_dma.o(i.DMA_PeriphIncOffsetSizeConfig), (24 bytes).
    Removing stm32f4xx_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f4xx_dma.o(i.DMA_StructInit), (34 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheCmd), (32 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheReset), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors), (92 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors), (92 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllSectors), (92 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseSector), (112 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetStatus), (60 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ITConfig), (28 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheCmd), (32 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheReset), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Lock), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BORConfig), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BootConfig), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetRDP), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Launch), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Lock), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config), (40 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig), (40 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPSelectionConfig), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_RDPConfig), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_UserConfig), (40 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRP1Config), (40 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRPConfig), (40 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_PrefetchBufferCmd), (32 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramByte), (56 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord), (64 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramHalfWord), (60 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramWord), (60 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_SetLatency), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_WaitForLastOperation), (34 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GetITStatus), (20 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_Init), (116 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_StructInit), (14 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ClearFlag), (42 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ClearITPendingBit), (48 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetECC), (18 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetFlagStatus), (40 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetITStatus), (52 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ITConfig), (86 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDCmd), (64 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDDeInit), (40 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDECCCmd), (64 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDInit), (104 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMDeInit), (48 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMStructInit), (48 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDCmd), (36 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDDeInit), (28 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDInit), (104 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f4xx_fsmc.o(.constdata), (28 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_DeInit), (340 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinLockConfig), (26 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputData), (6 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputData), (6 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit), (14 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_StructInit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ToggleBits), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_WriteBit), (12 bytes).
    Removing stm32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_AnalogFilterCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_CheckEvent), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ClearFlag), (6 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ClearITPendingBit), (6 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DeInit), (100 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DigitalFilterConfig), (16 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_FastModeDutyCycleConfig), (26 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetFlagStatus), (50 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetITStatus), (34 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetLastEvent), (14 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetPEC), (6 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ITConfig), (20 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Init), (188 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_NACKPositionConfig), (26 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_OwnAddress2Config), (16 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_PECPositionConfig), (26 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ReadRegister), (16 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ReceiveData), (6 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SMBusAlertConfig), (26 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SoftwareResetCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_StructInit), (28 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearFlag), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_DeInit), (84 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetFlagStatus), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetITStatus), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HCLKConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSEConfig), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_I2SCLKConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ITConfig), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEConfig), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEModeConfig), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO1Config), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO2Config), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK1Config), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK2Config), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLConfig), (36 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SConfig), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAIConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKConfig), (48 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SYSCLKConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp), (48 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_tim.o(i.TI1_Config), (46 bytes).
    Removing stm32f4xx_tim.o(i.TI2_Config), (54 bytes).
    Removing stm32f4xx_tim.o(i.TI3_Config), (50 bytes).
    Removing stm32f4xx_tim.o(i.TI4_Config), (54 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRConfig), (34 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxCmd), (22 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxNCmd), (22 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearITPendingBit), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC1Ref), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC2Ref), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC3Ref), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC4Ref), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CounterModeConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs), (28 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMACmd), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMAConfig), (8 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DeInit), (428 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode1Config), (32 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode2Config), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig), (50 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC1Config), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC2Config), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC3Config), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC4Config), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture1), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture2), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture3), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture4), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCounter), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetFlagStatus), (14 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetITStatus), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetPrescaler), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICInit), (98 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICStructInit), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_InternalClockConfig), (10 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1FastConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1Init), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1NPolarityConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PolarityConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PreloadConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2FastConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2Init), (136 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2NPolarityConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PolarityConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PreloadConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3FastConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3Init), (132 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3NPolarityConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PolarityConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PreloadConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4FastConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4Init), (100 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PolarityConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PreloadConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PWMIConfig), (110 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_RemapConfig), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectInputTrigger), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectMasterSlaveMode), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOCxM), (80 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOnePulseMode), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOutputTrigger), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectSlaveMode), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetClockDivision), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare4), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC1Prescaler), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC2Prescaler), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC3Prescaler), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC4Prescaler), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig), (48 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TimeBaseInit), (124 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearFlag), (8 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearITPendingBit), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockInit), (28 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f4xx_usart.o(i.USART_DMACmd), (20 bytes).
    Removing stm32f4xx_usart.o(i.USART_DeInit), (256 bytes).
    Removing stm32f4xx_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_ITConfig), (54 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDAConfig), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_OverSampling8Cmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetAddress), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_StructInit), (22 bytes).
    Removing stm32f4xx_usart.o(i.USART_WakeUpConfig), (16 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing misc.o(i.NVIC_Init), (104 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (28 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f4xx_spi.o(i.I2S_FullDuplexConfig), (58 bytes).
    Removing stm32f4xx_spi.o(i.I2S_Init), (240 bytes).
    Removing stm32f4xx_spi.o(i.I2S_StructInit), (18 bytes).
    Removing stm32f4xx_spi.o(i.SPI_BiDirectionalLineConfig), (26 bytes).
    Removing stm32f4xx_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_DataSizeConfig), (16 bytes).
    Removing stm32f4xx_spi.o(i.SPI_GetCRC), (12 bytes).
    Removing stm32f4xx_spi.o(i.SPI_GetCRCPolynomial), (4 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ClearITPendingBit), (14 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_DMACmd), (20 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_DeInit), (196 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_GetITStatus), (44 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ITConfig), (28 bytes).
    Removing stm32f4xx_spi.o(i.SPI_NSSInternalSoftwareConfig), (28 bytes).
    Removing stm32f4xx_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_TIModeCmd), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_CompensationCellCmd), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_DeInit), (24 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig), (44 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_GetCompensationCellStatus), (20 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemoryRemapConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemorySwappingBank), (12 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(i.CAN_CancelTransmit), (42 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearFlag), (48 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearITPendingBit), (136 bytes).
    Removing stm32f4xx_can.o(i.CAN_DBGFreeze), (24 bytes).
    Removing stm32f4xx_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f4xx_can.o(i.CAN_FIFORelease), (24 bytes).
    Removing stm32f4xx_can.o(i.CAN_FilterInit), (212 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetFlagStatus), (82 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetITStatus), (204 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLSBTransmitErrorCounter), (8 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLastErrorCode), (10 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetReceiveErrorCounter), (6 bytes).
    Removing stm32f4xx_can.o(i.CAN_ITConfig), (20 bytes).
    Removing stm32f4xx_can.o(i.CAN_Init), (260 bytes).
    Removing stm32f4xx_can.o(i.CAN_MessagePending), (28 bytes).
    Removing stm32f4xx_can.o(i.CAN_OperatingModeRequest), (154 bytes).
    Removing stm32f4xx_can.o(i.CAN_Receive), (130 bytes).
    Removing stm32f4xx_can.o(i.CAN_SlaveStartBank), (44 bytes).
    Removing stm32f4xx_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f4xx_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f4xx_can.o(i.CAN_TTComModeCmd), (96 bytes).
    Removing stm32f4xx_can.o(i.CAN_Transmit), (182 bytes).
    Removing stm32f4xx_can.o(i.CAN_TransmitStatus), (136 bytes).
    Removing stm32f4xx_can.o(i.CAN_WakeUp), (40 bytes).
    Removing stm32f4xx_can.o(i.CheckITStatus), (12 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (132 bytes).
    Removing arm_cfft_radix2_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_radix2_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_radix2_f32.o(.text), (760 bytes).
    Removing arm_cfft_radix2_init_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_radix2_init_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_radix2_init_f32.o(.text), (308 bytes).
    Removing arm_bitreversal.o(.rev16_text), (4 bytes).
    Removing arm_bitreversal.o(.revsh_text), (4 bytes).
    Removing arm_bitreversal.o(.text), (486 bytes).
    Removing arm_common_tables.o(.rev16_text), (4 bytes).
    Removing arm_common_tables.o(.revsh_text), (4 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (512 bytes).
    Removing arm_common_tables.o(.constdata), (1024 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (4096 bytes).
    Removing arm_common_tables.o(.constdata), (8192 bytes).
    Removing arm_common_tables.o(.constdata), (16384 bytes).
    Removing arm_common_tables.o(.constdata), (32768 bytes).
    Removing arm_common_tables.o(.constdata), (24576 bytes).
    Removing arm_common_tables.o(.constdata), (12288 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (40 bytes).
    Removing arm_common_tables.o(.constdata), (96 bytes).
    Removing arm_common_tables.o(.constdata), (112 bytes).
    Removing arm_common_tables.o(.constdata), (416 bytes).
    Removing arm_common_tables.o(.constdata), (880 bytes).
    Removing arm_common_tables.o(.constdata), (896 bytes).
    Removing arm_common_tables.o(.constdata), (3600 bytes).
    Removing arm_common_tables.o(.constdata), (7616 bytes).
    Removing arm_common_tables.o(.constdata), (8064 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (512 bytes).
    Removing arm_common_tables.o(.constdata), (1024 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (4096 bytes).
    Removing arm_common_tables.o(.constdata), (8192 bytes).
    Removing arm_common_tables.o(.constdata), (16384 bytes).
    Removing tft_lcd.o(i.LCD_REG_Select), (8 bytes).

762 unused section(s) (total 287380 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_hexfp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_infnan.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dfixull.s                       0x00000000   Number         0  dfixull.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_umaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpconst.s                       0x00000000   Number         0  fpconst.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/scanf1.s                        0x00000000   Number         0  scanf1.o ABSOLUTE
    ../fplib/scanf2.s                        0x00000000   Number         0  scanf2.o ABSOLUTE
    ../fplib/scanf2a.s                       0x00000000   Number         0  scanf2a.o ABSOLUTE
    ../fplib/scanf2b.s                       0x00000000   Number         0  scanf2b.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atof.c                        0x00000000   Number         0  atof.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos_x.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos.o ABSOLUTE
    ../mathlib/cos_i.c                       0x00000000   Number         0  cos_i.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/frexp.c                       0x00000000   Number         0  frexp.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp_x.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp.o ABSOLUTE
    ../mathlib/narrow.c                      0x00000000   Number         0  narrow.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin_x.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i_x.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf_x.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ..\CommonTables\arm_common_tables.c      0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\TransformFunctions\arm_bitreversal.c  0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    ..\TransformFunctions\arm_cfft_radix2_f32.c 0x00000000   Number         0  arm_cfft_radix2_f32.o ABSOLUTE
    ..\TransformFunctions\arm_cfft_radix2_init_f32.c 0x00000000   Number         0  arm_cfft_radix2_init_f32.o ABSOLUTE
    ..\\CommonTables\\arm_common_tables.c    0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\\TransformFunctions\\arm_bitreversal.c 0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    ..\\TransformFunctions\\arm_cfft_radix2_f32.c 0x00000000   Number         0  arm_cfft_radix2_f32.o ABSOLUTE
    ..\\TransformFunctions\\arm_cfft_radix2_init_f32.c 0x00000000   Number         0  arm_cfft_radix2_init_f32.o ABSOLUTE
    _01_App\App_LED.c                        0x00000000   Number         0  app_led.o ABSOLUTE
    _01_App\App_Touch.c                      0x00000000   Number         0  app_touch.o ABSOLUTE
    _01_App\User.c                           0x00000000   Number         0  user.o ABSOLUTE
    _01_App\\App_LED.c                       0x00000000   Number         0  app_led.o ABSOLUTE
    _01_App\\App_Touch.c                     0x00000000   Number         0  app_touch.o ABSOLUTE
    _01_App\\User.c                          0x00000000   Number         0  user.o ABSOLUTE
    _02_Core\\stm32f4xx_it.c                 0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    _02_Core\\system_stm32f4xx.c             0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    _02_Core\startup_stm32f40_41xxx.s        0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    _02_Core\stm32f4xx_it.c                  0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    _02_Core\system_stm32f4xx.c              0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    _03_Drive\Drive_ADS1256.c                0x00000000   Number         0  drive_ads1256.o ABSOLUTE
    _03_Drive\Drive_Communication.c          0x00000000   Number         0  drive_communication.o ABSOLUTE
    _03_Drive\Drive_DAC.c                    0x00000000   Number         0  drive_dac.o ABSOLUTE
    _03_Drive\Drive_DMA.c                    0x00000000   Number         0  drive_dma.o ABSOLUTE
    _03_Drive\Drive_FFT.c                    0x00000000   Number         0  drive_fft.o ABSOLUTE
    _03_Drive\Drive_GPIO.c                   0x00000000   Number         0  drive_gpio.o ABSOLUTE
    _03_Drive\Drive_PS2.c                    0x00000000   Number         0  drive_ps2.o ABSOLUTE
    _03_Drive\Drive_PWM.c                    0x00000000   Number         0  drive_pwm.o ABSOLUTE
    _03_Drive\Drive_Timer.c                  0x00000000   Number         0  drive_timer.o ABSOLUTE
    _03_Drive\Drive_Touch.c                  0x00000000   Number         0  drive_touch.o ABSOLUTE
    _03_Drive\Drive_TouchKey.c               0x00000000   Number         0  drive_touchkey.o ABSOLUTE
    _03_Drive\User_AD8370.c                  0x00000000   Number         0  user_ad8370.o ABSOLUTE
    _03_Drive\User_ADC.c                     0x00000000   Number         0  user_adc.o ABSOLUTE
    _03_Drive\User_BGD.c                     0x00000000   Number         0  user_bgd.o ABSOLUTE
    _03_Drive\User_DAC.c                     0x00000000   Number         0  user_dac.o ABSOLUTE
    _03_Drive\User_DAC8562.c                 0x00000000   Number         0  user_dac8562.o ABSOLUTE
    _03_Drive\User_IIC.c                     0x00000000   Number         0  user_iic.o ABSOLUTE
    _03_Drive\User_PGA2310.c                 0x00000000   Number         0  user_pga2310.o ABSOLUTE
    _03_Drive\User_SPI.c                     0x00000000   Number         0  user_spi.o ABSOLUTE
    _03_Drive\\Drive_ADS1256.c               0x00000000   Number         0  drive_ads1256.o ABSOLUTE
    _03_Drive\\Drive_Communication.c         0x00000000   Number         0  drive_communication.o ABSOLUTE
    _03_Drive\\Drive_DAC.c                   0x00000000   Number         0  drive_dac.o ABSOLUTE
    _03_Drive\\Drive_DMA.c                   0x00000000   Number         0  drive_dma.o ABSOLUTE
    _03_Drive\\Drive_FFT.c                   0x00000000   Number         0  drive_fft.o ABSOLUTE
    _03_Drive\\Drive_GPIO.c                  0x00000000   Number         0  drive_gpio.o ABSOLUTE
    _03_Drive\\Drive_PS2.c                   0x00000000   Number         0  drive_ps2.o ABSOLUTE
    _03_Drive\\Drive_PWM.c                   0x00000000   Number         0  drive_pwm.o ABSOLUTE
    _03_Drive\\Drive_Timer.c                 0x00000000   Number         0  drive_timer.o ABSOLUTE
    _03_Drive\\Drive_Touch.c                 0x00000000   Number         0  drive_touch.o ABSOLUTE
    _03_Drive\\Drive_TouchKey.c              0x00000000   Number         0  drive_touchkey.o ABSOLUTE
    _03_Drive\\User_AD8370.c                 0x00000000   Number         0  user_ad8370.o ABSOLUTE
    _03_Drive\\User_ADC.c                    0x00000000   Number         0  user_adc.o ABSOLUTE
    _03_Drive\\User_BGD.c                    0x00000000   Number         0  user_bgd.o ABSOLUTE
    _03_Drive\\User_DAC.c                    0x00000000   Number         0  user_dac.o ABSOLUTE
    _03_Drive\\User_DAC8562.c                0x00000000   Number         0  user_dac8562.o ABSOLUTE
    _03_Drive\\User_IIC.c                    0x00000000   Number         0  user_iic.o ABSOLUTE
    _03_Drive\\User_PGA2310.c                0x00000000   Number         0  user_pga2310.o ABSOLUTE
    _03_Drive\\User_SPI.c                    0x00000000   Number         0  user_spi.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\misc.c     0x00000000   Number         0  misc.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_adc.c 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_can.c 0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_crc.c 0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_dac.c 0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_dma.c 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_exti.c 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_flash.c 0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_fsmc.c 0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_gpio.c 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_i2c.c 0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_rcc.c 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_spi.c 0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_syscfg.c 0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_tim.c 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    _04_FWLib\STM32F40x_FWLib\src\stm32f4xx_usart.c 0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\misc.c  0x00000000   Number         0  misc.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_adc.c 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_can.c 0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_crc.c 0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_dac.c 0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_dma.c 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_exti.c 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_flash.c 0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_fsmc.c 0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_gpio.c 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_i2c.c 0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_rcc.c 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_spi.c 0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_syscfg.c 0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_tim.c 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    _04_FWLib\\STM32F40x_FWLib\\src\\stm32f4xx_usart.c 0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    _05_Os\Os_UI.c                           0x00000000   Number         0  os_ui.o ABSOLUTE
    _05_Os\Os_cpu.c                          0x00000000   Number         0  os_cpu.o ABSOLUTE
    _05_Os\Os_malloc.c                       0x00000000   Number         0  os_malloc.o ABSOLUTE
    _05_Os\\Os_UI.c                          0x00000000   Number         0  os_ui.o ABSOLUTE
    _05_Os\\Os_cpu.c                         0x00000000   Number         0  os_cpu.o ABSOLUTE
    _05_Os\\Os_malloc.c                      0x00000000   Number         0  os_malloc.o ABSOLUTE
    _05_Os\core.asm                          0x00000000   Number         0  core.o ABSOLUTE
    _06_System\\delay.c                      0x00000000   Number         0  delay.o ABSOLUTE
    _06_System\\sys.c                        0x00000000   Number         0  sys.o ABSOLUTE
    _06_System\\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    _06_System\delay.c                       0x00000000   Number         0  delay.o ABSOLUTE
    _06_System\sys.c                         0x00000000   Number         0  sys.o ABSOLUTE
    _06_System\usart.c                       0x00000000   Number         0  usart.o ABSOLUTE
    _07_TFT_LCD\Character.c                  0x00000000   Number         0  character.o ABSOLUTE
    _07_TFT_LCD\TFT_LCD.c                    0x00000000   Number         0  tft_lcd.o ABSOLUTE
    _07_TFT_LCD\W25Q64.c                     0x00000000   Number         0  w25q64.o ABSOLUTE
    _07_TFT_LCD\\Character.c                 0x00000000   Number         0  character.o ABSOLUTE
    _07_TFT_LCD\\TFT_LCD.c                   0x00000000   Number         0  tft_lcd.o ABSOLUTE
    _07_TFT_LCD\\W25Q64.c                    0x00000000   Number         0  w25q64.o ABSOLUTE
    _07_TFT_LCD\\fontupd.c                   0x00000000   Number         0  fontupd.o ABSOLUTE
    _07_TFT_LCD\\spi.c                       0x00000000   Number         0  spi.o ABSOLUTE
    _07_TFT_LCD\\text.c                      0x00000000   Number         0  text.o ABSOLUTE
    _07_TFT_LCD\fonts.c                      0x00000000   Number         0  fonts.o ABSOLUTE
    _07_TFT_LCD\fontupd.c                    0x00000000   Number         0  fontupd.o ABSOLUTE
    _07_TFT_LCD\spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    _07_TFT_LCD\text.c                       0x00000000   Number         0  text.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x080001fc   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x08000202   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000208   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800020e   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000017  0x08000214   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000218   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800021a   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x0800021e   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000224   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000224   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000224   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000224   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800022e   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000230   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000232   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000234   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000234   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000234   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800023a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800023a   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800023e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800023e   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000246   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000248   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000248   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800024c   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000254   Section       60  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x08000254   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08000290   Section        2  use_no_semi_2.o(.text)
    .text                                    0x08000294   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x080002bc   Section        0  _printf_pad.o(.text)
    .text                                    0x0800030c   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000494   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x080004f8   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000546   Section        0  heapauxi.o(.text)
    .text                                    0x0800054c   Section        2  use_no_semi.o(.text)
    .text                                    0x0800054e   Section        0  _rserrno.o(.text)
    .text                                    0x08000564   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000567   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000984   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x08000c80   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000c81   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000cb0   Section        0  _sputc.o(.text)
    .text                                    0x08000cbc   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08000cc4   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000ccc   Section      138  lludiv10.o(.text)
    .text                                    0x08000d58   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08000dd8   Section        0  bigflt0.o(.text)
    .text                                    0x08000ebc   Section        8  libspace.o(.text)
    .text                                    0x08000ec4   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000f0e   Section        0  exit.o(.text)
    .text                                    0x08000f20   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x08000fa0   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08000fde   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08001024   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08001084   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x080013bc   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08001498   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x080014c2   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x080014ec   Section      580  btod.o(CL$$btod_mult_common)
    CODE                                     0x08001730   Section      124  core.o(CODE)
    $v0                                      0x08001730   Number         0  core.o(CODE)
    i.ADS1256ReadData                        0x080017ac   Section        0  drive_ads1256.o(i.ADS1256ReadData)
    i.ADS1256WREG                            0x0800181c   Section        0  drive_ads1256.o(i.ADS1256WREG)
    i.ADS1256_Init                           0x0800186c   Section        0  drive_ads1256.o(i.ADS1256_Init)
    i.BusFault_Handler                       0x0800190c   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.Change_Menu                            0x08001910   Section        0  user.o(i.Change_Menu)
    i.DDSDataInit                            0x08001980   Section        0  drive_communication.o(i.DDSDataInit)
    i.DMA2_Stream0_IRQHandler                0x08001a34   Section        0  user_adc.o(i.DMA2_Stream0_IRQHandler)
    i.DMA_ClearFlag                          0x08001a60   Section        0  stm32f4xx_dma.o(i.DMA_ClearFlag)
    i.DebugMon_Handler                       0x08001a8c   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Disp_Main                              0x08001a90   Section        0  user.o(i.Disp_Main)
    i.FSMC_NORSRAMCmd                        0x08001b8c   Section        0  stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd)
    i.FSMC_NORSRAMInit                       0x08001bb0   Section        0  stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit)
    i.GPIO_Change_Init                       0x08001c7c   Section        0  user.o(i.GPIO_Change_Init)
    i.GPIO_Init                              0x08001cb0   Section        0  stm32f4xx_gpio.o(i.GPIO_Init)
    i.GPIO_PinAFConfig                       0x08001d2c   Section        0  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    i.GPIO_ReadInputDataBit                  0x08001d4c   Section        0  stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x08001d5a   Section        0  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08001d5e   Section        0  stm32f4xx_gpio.o(i.GPIO_SetBits)
    i.Get_Val                                0x08001d64   Section        0  drive_ads1256.o(i.Get_Val)
    i.HardFault_Handler                      0x08001d9c   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.Init_ADS1256_GPIO                      0x08001da0   Section        0  drive_ads1256.o(i.Init_ADS1256_GPIO)
    i.Init_All                               0x08001e00   Section        0  user.o(i.Init_All)
    i.Init_Uart                              0x08001e20   Section        0  drive_communication.o(i.Init_Uart)
    i.Key_StateSweep                         0x08001ea8   Section        0  drive_ps2.o(i.Key_StateSweep)
    i.LCD_Appoint_Clear                      0x08001f4e   Section        0  tft_lcd.o(i.LCD_Appoint_Clear)
    i.LCD_Clear                              0x08001f98   Section        0  tft_lcd.o(i.LCD_Clear)
    i.LCD_CtrlLinesConfig                    0x08001fbc   Section        0  tft_lcd.o(i.LCD_CtrlLinesConfig)
    i.LCD_DrawRectS                          0x08002114   Section        0  tft_lcd.o(i.LCD_DrawRectS)
    i.LCD_DrawuniLine                        0x08002148   Section        0  tft_lcd.o(i.LCD_DrawuniLine)
    i.LCD_FSMCConfig                         0x08002200   Section        0  tft_lcd.o(i.LCD_FSMCConfig)
    i.LCD_SetCursor                          0x08002256   Section        0  tft_lcd.o(i.LCD_SetCursor)
    i.LCD_SetDisplayWindow                   0x08002284   Section        0  tft_lcd.o(i.LCD_SetDisplayWindow)
    i.LCD_WriteRAM                           0x080022b4   Section        0  tft_lcd.o(i.LCD_WriteRAM)
    i.LCD_WriteRAM_Prepare                   0x080022bc   Section        0  tft_lcd.o(i.LCD_WriteRAM_Prepare)
    i.LED_Control                            0x080022c4   Section        0  app_led.o(i.LED_Control)
    i.LED_Init                               0x08002338   Section        0  drive_gpio.o(i.LED_Init)
    i.LED_main                               0x08002388   Section        0  app_led.o(i.LED_main)
    i.Line_fitRead                           0x080023a8   Section        0  user.o(i.Line_fitRead)
    i.Line_fitWrite                          0x080023e8   Section        0  user.o(i.Line_fitWrite)
    i.MemManage_Handler                      0x08002428   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.MenuHaddler_1                          0x0800242c   Section        0  user.o(i.MenuHaddler_1)
    i.MenuHaddler_2                          0x080025cc   Section        0  user.o(i.MenuHaddler_2)
    i.MenuHaddler_3                          0x0800286c   Section        0  user.o(i.MenuHaddler_3)
    i.MenuHaddler_4                          0x08002880   Section        0  user.o(i.MenuHaddler_4)
    i.Moving_Average_Filter                  0x08002894   Section        0  drive_ads1256.o(i.Moving_Average_Filter)
    i.MyPs2KeyScan                           0x080028ec   Section        0  drive_ps2.o(i.MyPs2KeyScan)
    i.NMI_Handler                            0x08002a70   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.OSDelPrioRdy                           0x08002a74   Section        0  os_cpu.o(i.OSDelPrioRdy)
    i.OSGetHighRdy                           0x08002a88   Section        0  os_cpu.o(i.OSGetHighRdy)
    i.OSSetPrioRdy                           0x08002aac   Section        0  os_cpu.o(i.OSSetPrioRdy)
    i.OSTimeDly                              0x08002ac0   Section        0  os_cpu.o(i.OSTimeDly)
    i.OS_Char_Show                           0x08002afc   Section        0  os_ui.o(i.OS_Char_Show)
    i.OS_Font_Show                           0x08002bd4   Section        0  os_ui.o(i.OS_Font_Show)
    i.OS_HzMat_Get                           0x08002c84   Section        0  os_ui.o(i.OS_HzMat_Get)
    i.OS_IDLE_Task                           0x08002d20   Section        0  os_cpu.o(i.OS_IDLE_Task)
    i.OS_Init                                0x08002d22   Section        0  main.o(i.OS_Init)
    i.OS_LCD_Init                            0x08002d38   Section        0  os_ui.o(i.OS_LCD_Init)
    i.OS_Line_Draw                           0x08002d88   Section        0  os_ui.o(i.OS_Line_Draw)
    i.OS_Num_Show                            0x08002e34   Section        0  os_ui.o(i.OS_Num_Show)
    i.OS_Point_Draw                          0x08002e94   Section        0  os_ui.o(i.OS_Point_Draw)
    i.OS_Rect_Draw                           0x08002eac   Section        0  os_ui.o(i.OS_Rect_Draw)
    i.OS_Sched                               0x08002f34   Section        0  os_cpu.o(i.OS_Sched)
    i.OS_SchedLock                           0x08002f78   Section        0  os_cpu.o(i.OS_SchedLock)
    i.OS_SchedUnlock                         0x08002f90   Section        0  os_cpu.o(i.OS_SchedUnlock)
    i.OS_Start                               0x08002fa8   Section        0  os_cpu.o(i.OS_Start)
    i.OS_String_Show                         0x08002ff8   Section        0  os_ui.o(i.OS_String_Show)
    i.PS2_GPIO_Init                          0x08003068   Section        0  drive_ps2.o(i.PS2_GPIO_Init)
    PS2_GPIO_Init                            0x08003069   Thumb Code    44  drive_ps2.o(i.PS2_GPIO_Init)
    i.PS2_Keyboard_Init                      0x08003098   Section        0  drive_ps2.o(i.PS2_Keyboard_Init)
    i.PS2_ReadKeyCodon                       0x0800309c   Section        0  drive_ps2.o(i.PS2_ReadKeyCodon)
    i.PS2_ReadNum                            0x08003148   Section        0  user.o(i.PS2_ReadNum)
    i.PS2_SCL_Set                            0x08003290   Section        0  drive_ps2.o(i.PS2_SCL_Set)
    PS2_SCL_Set                              0x08003291   Thumb Code    86  drive_ps2.o(i.PS2_SCL_Set)
    i.PS2_SCL_Wait                           0x080032ec   Section        0  drive_ps2.o(i.PS2_SCL_Wait)
    PS2_SCL_Wait                             0x080032ed   Thumb Code   108  drive_ps2.o(i.PS2_SCL_Wait)
    i.PutPixel                               0x08003360   Section        0  tft_lcd.o(i.PutPixel)
    i.RCC_AHB1PeriphClockCmd                 0x0800337c   Section        0  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    i.RCC_AHB3PeriphClockCmd                 0x08003398   Section        0  stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x080033b4   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_APB2PeriphResetCmd                 0x080033d0   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd)
    i.RCC_GetClocksFreq                      0x080033ec   Section        0  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    i.SPI1_Init                              0x0800348c   Section        0  spi.o(i.SPI1_Init)
    i.SPI1_ReadWriteByte                     0x08003564   Section        0  spi.o(i.SPI1_ReadWriteByte)
    i.SPI1_SetSpeed                          0x08003598   Section        0  spi.o(i.SPI1_SetSpeed)
    i.SPI_Cmd                                0x080035b4   Section        0  stm32f4xx_spi.o(i.SPI_Cmd)
    i.SPI_I2S_GetFlagStatus                  0x080035cc   Section        0  stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus)
    i.SPI_I2S_ReceiveData                    0x080035da   Section        0  stm32f4xx_spi.o(i.SPI_I2S_ReceiveData)
    i.SPI_I2S_SendData                       0x080035de   Section        0  stm32f4xx_spi.o(i.SPI_I2S_SendData)
    i.SPI_Init                               0x080035e2   Section        0  stm32f4xx_spi.o(i.SPI_Init)
    i.SPI_ReadByte                           0x0800361c   Section        0  drive_ads1256.o(i.SPI_ReadByte)
    i.SPI_WriteByte                          0x08003668   Section        0  drive_ads1256.o(i.SPI_WriteByte)
    i.SVC_Handler                            0x080036bc   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SetSysClock                            0x080036c0   Section        0  system_stm32f4xx.o(i.SetSysClock)
    SetSysClock                              0x080036c1   Thumb Code   168  system_stm32f4xx.o(i.SetSysClock)
    i.Show_Val                               0x08003778   Section        0  user.o(i.Show_Val)
    i.SysTick_CLKSourceConfig                0x080037f0   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x0800380c   Section        0  os_cpu.o(i.SysTick_Handler)
    i.SystemInit                             0x08003868   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.System_init                            0x080038c4   Section        0  os_cpu.o(i.System_init)
    i.TFT_LCD_Init                           0x08003910   Section        0  tft_lcd.o(i.TFT_LCD_Init)
    i.TIM_Cmd                                0x08003a6e   Section        0  stm32f4xx_tim.o(i.TIM_Cmd)
    i.Task_Create                            0x08003a88   Section        0  os_cpu.o(i.Task_Create)
    i.Task_End                               0x08003b10   Section        0  os_cpu.o(i.Task_End)
    i.USART1_IRQHandler                      0x08003b14   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART_Cmd                              0x08003b7c   Section        0  stm32f4xx_usart.o(i.USART_Cmd)
    i.USART_GetFlagStatus                    0x08003b94   Section        0  stm32f4xx_usart.o(i.USART_GetFlagStatus)
    i.USART_GetITStatus                      0x08003ba2   Section        0  stm32f4xx_usart.o(i.USART_GetITStatus)
    i.USART_Init                             0x08003be4   Section        0  stm32f4xx_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08003ca0   Section        0  stm32f4xx_usart.o(i.USART_ReceiveData)
    i.USART_SendData                         0x08003ca8   Section        0  stm32f4xx_usart.o(i.USART_SendData)
    i.UsageFault_Handler                     0x08003cb0   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.User_main                              0x08003cb4   Section        0  user.o(i.User_main)
    i.W25Q64_Erase_Sector                    0x08003d08   Section        0  w25q64.o(i.W25Q64_Erase_Sector)
    i.W25Q64_Init                            0x08003d54   Section        0  w25q64.o(i.W25Q64_Init)
    i.W25Q64_Read                            0x08003dcc   Section        0  w25q64.o(i.W25Q64_Read)
    i.W25Q64_ReadID                          0x08003e28   Section        0  w25q64.o(i.W25Q64_ReadID)
    i.W25Q64_ReadSR                          0x08003e7c   Section        0  w25q64.o(i.W25Q64_ReadSR)
    i.W25Q64_Wait_Busy                       0x08003eac   Section        0  w25q64.o(i.W25Q64_Wait_Busy)
    i.W25Q64_Write                           0x08003eb8   Section        0  w25q64.o(i.W25Q64_Write)
    i.W25Q64_Write_Enable                    0x08003f5c   Section        0  w25q64.o(i.W25Q64_Write_Enable)
    i.W25Q64_Write_NoCheck                   0x08003f84   Section        0  w25q64.o(i.W25Q64_Write_NoCheck)
    i.W25Q64_Write_Page                      0x08003fc4   Section        0  w25q64.o(i.W25Q64_Write_Page)
    i.__ARM_fpclassify                       0x08004024   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp_pow                           0x08004058   Section        0  pow.o(i.__hardfp_pow)
    i.__hardfp_sqrt                          0x08004ca8   Section        0  sqrt.o(i.__hardfp_sqrt)
    i.__kernel_poly                          0x08004d22   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_divzero                  0x08004e20   Section        0  dunder.o(i.__mathlib_dbl_divzero)
    i.__mathlib_dbl_infnan2                  0x08004e50   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x08004e68   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_overflow                 0x08004e88   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x08004ea8   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i._is_digit                              0x08004ec8   Section        0  __printf_wp.o(i._is_digit)
    i._sys_exit                              0x08004ed6   Section        0  usart.o(i._sys_exit)
    i.calculate_paper_count                  0x08004ed8   Section        0  user.o(i.calculate_paper_count)
    i.crc_16                                 0x08004f80   Section        0  drive_communication.o(i.crc_16)
    crc_16                                   0x08004f81   Thumb Code    52  drive_communication.o(i.crc_16)
    i.delay_ms                               0x08004fb4   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x08004ff0   Section        0  delay.o(i.delay_us)
    i.fabs                                   0x0800503c   Section        0  fabs.o(i.fabs)
    i.find_calibrated_interval               0x08005054   Section        0  user.o(i.find_calibrated_interval)
    i.font_init                              0x080051ac   Section        0  fontupd.o(i.font_init)
    i.main                                   0x080051ec   Section        0  main.o(i.main)
    i.sendData                               0x08005230   Section        0  drive_communication.o(i.sendData)
    i.sqrt                                   0x0800533c   Section        0  sqrt.o(i.sqrt)
    i.usartSendData                          0x080053ac   Section        0  drive_communication.o(i.usartSendData)
    usartSendData                            0x080053ad   Thumb Code    46  drive_communication.o(i.usartSendData)
    locale$$code                             0x080053e0   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$basic                              0x0800540c   Section       24  basic.o(x$fpl$basic)
    $v0                                      0x0800540c   Number         0  basic.o(x$fpl$basic)
    x$fpl$d2f                                0x08005424   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x08005424   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x08005488   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x08005488   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08005499   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcheck1                            0x080055d8   Section       16  dcheck1.o(x$fpl$dcheck1)
    $v0                                      0x080055d8   Number         0  dcheck1.o(x$fpl$dcheck1)
    x$fpl$dcmpinf                            0x080055e8   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x080055e8   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x08005600   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x08005600   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x08005607   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dflt                               0x080058b0   Section       46  dflt_clz.o(x$fpl$dflt)
    $v0                                      0x080058b0   Number         0  dflt_clz.o(x$fpl$dflt)
    x$fpl$dfltu                              0x080058de   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x080058de   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dleqf                              0x08005904   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x08005904   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x0800597c   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x0800597c   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08005ad0   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x08005ad0   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08005b6c   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08005b6c   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x08005b78   Section      108  drleqf.o(x$fpl$drleqf)
    $v0                                      0x08005b78   Number         0  drleqf.o(x$fpl$drleqf)
    x$fpl$drsb                               0x08005be4   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x08005be4   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x08005bfc   Section      408  dsqrt_umaal.o(x$fpl$dsqrt)
    $v0                                      0x08005bfc   Number         0  dsqrt_umaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x08005d94   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x08005d94   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x08005da5   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x08005f68   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x08005f68   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x08005fbe   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x08005fbe   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x0800604a   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800604a   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x08006054   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x08006054   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$printf1                            0x0800605e   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x0800605e   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x08006062   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x08006062   Number         0  printf2.o(x$fpl$printf2)
    x$fpl$retnan                             0x08006066   Section      100  retnan.o(x$fpl$retnan)
    $v0                                      0x08006066   Number         0  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x080060ca   Section       92  scalbn.o(x$fpl$scalbn)
    $v0                                      0x080060ca   Number         0  scalbn.o(x$fpl$scalbn)
    x$fpl$trapveneer                         0x08006126   Section       48  trapv.o(x$fpl$trapveneer)
    $v0                                      0x08006126   Number         0  trapv.o(x$fpl$trapveneer)
    .constdata                               0x08006156   Section     6080  fonts.o(.constdata)
    x$fpl$usenofp                            0x08006156   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x08007916   Section     1520  fonts.o(.constdata)
    .constdata                               0x08007f06   Section     3420  fonts.o(.constdata)
    .constdata                               0x08008c62   Section     6080  fonts.o(.constdata)
    .constdata                               0x0800a422   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x0800a422   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x0800a438   Section      136  pow.o(.constdata)
    bp                                       0x0800a438   Data          16  pow.o(.constdata)
    dp_h                                     0x0800a448   Data          16  pow.o(.constdata)
    dp_l                                     0x0800a458   Data          16  pow.o(.constdata)
    L                                        0x0800a468   Data          48  pow.o(.constdata)
    P                                        0x0800a498   Data          40  pow.o(.constdata)
    .constdata                               0x0800a4c0   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x0800a4c0   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x0800a4d3   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x0800a4e8   Section        8  qnan.o(.constdata)
    .constdata                               0x0800a4f0   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x0800a4f0   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x0800a52c   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x0800a5a4   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x0800a5a8   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x0800a5b0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0800a5bc   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0800a5be   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0800a5bf   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x0800a5c0   Data           0  lc_numeric_c.o(locale$$data)
    .ARM.__AT_0x10000000                     0x10000000   Section    61440  os_malloc.o(.ARM.__AT_0x10000000)
    .ARM.__AT_0x1000F000                     0x1000f000   Section     3840  os_malloc.o(.ARM.__AT_0x1000F000)
    .data                                    0x20000000   Section        1  app_led.o(.data)
    _led_cnt                                 0x20000000   Data           1  app_led.o(.data)
    .data                                    0x20000001   Section        1  user_adc.o(.data)
    .data                                    0x20000002   Section        1  user.o(.data)
    .data                                    0x20000004   Section       24  os_cpu.o(.data)
    .data                                    0x2000001c   Section        4  os_cpu.o(.data)
    .data                                    0x20000020   Section        2  usart.o(.data)
    .data                                    0x20000024   Section        8  drive_ps2.o(.data)
    .data                                    0x2000002c   Section        8  fonts.o(.data)
    .data                                    0x20000034   Section        8  tft_lcd.o(.data)
    LCD_Currentfonts                         0x20000038   Data           4  tft_lcd.o(.data)
    .data                                    0x2000003c   Section        2  w25q64.o(.data)
    .data                                    0x2000003e   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x2000003e   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x20000050   Section       20  system_stm32f4xx.o(.data)
    .bss                                     0x20000064   Section      500  user.o(.bss)
    .bss                                     0x20000258   Section    38304  main.o(.bss)
    .bss                                     0x200097f8   Section     1152  os_cpu.o(.bss)
    .bss                                     0x20009c78   Section      200  usart.o(.bss)
    .bss                                     0x20009d40   Section       10  drive_ps2.o(.bss)
    .bss                                     0x20009d4c   Section      556  drive_communication.o(.bss)
    .bss                                     0x20009f78   Section       33  fontupd.o(.bss)
    .bss                                     0x20009f99   Section     4096  w25q64.o(.bss)
    .bss                                     0x2000af9c   Section       96  libspace.o(.bss)
    HEAP                                     0x2000b000   Section        0  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x2000b000   Section    32768  startup_stm32f40_41xxx.o(STACK)
    Heap_Mem                                 0x2000b000   Data           0  startup_stm32f40_41xxx.o(HEAP)
    Stack_Mem                                0x2000b000   Data       32768  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x20013000   Data           0  startup_stm32f40_41xxx.o(STACK)
    .ARM.__AT_0x68000000                     0x68000000   Section    983040  os_malloc.o(.ARM.__AT_0x68000000)
    .ARM.__AT_0x680F0000                     0x680f0000   Section    61440  os_malloc.o(.ARM.__AT_0x680F0000)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x080001fd   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_e                                0x08000203   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000209   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800020f   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_percent_end                      0x08000215   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000219   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800021b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000231   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000235   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000235   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000235   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800023b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800023b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800023f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800023f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000247   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000249   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000249   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800024d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000255   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x0800026f   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x08000271   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __use_no_semihosting                     0x08000291   Thumb Code     2  use_no_semi_2.o(.text)
    __2sprintf                               0x08000295   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_pre_padding                      0x080002bd   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x080002e9   Thumb Code    34  _printf_pad.o(.text)
    __printf                                 0x0800030d   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    __aeabi_memcpy4                          0x08000495   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000495   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000495   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x080004dd   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr4                          0x080004f9   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080004f9   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080004f9   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080004fd   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x08000547   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000549   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800054b   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x0800054d   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0800054d   Thumb Code     2  use_no_semi.o(.text)
    __read_errno                             0x0800054f   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08000559   Thumb Code    12  _rserrno.o(.text)
    __lib_sel_fp_printf                      0x08000565   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000717   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x08000985   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_char_common                      0x08000c8b   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000cb1   Thumb Code    10  _sputc.o(.text)
    __rt_locale                              0x08000cbd   Thumb Code     8  rt_locale_intlibspace.o(.text)
    __aeabi_errno_addr                       0x08000cc5   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000cc5   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000cc5   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x08000ccd   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_infnan                        0x08000d59   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08000dd9   Thumb Code   224  bigflt0.o(.text)
    __user_libspace                          0x08000ebd   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000ebd   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000ebd   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08000ec5   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000f0f   Thumb Code    18  exit.o(.text)
    strcmp                                   0x08000f21   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x08000fa1   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08000fdf   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08001025   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08001085   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x080013bd   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08001499   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x080014c3   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x080014ed   Thumb Code   580  btod.o(CL$$btod_mult_common)
    OS_CPU_SR_Save                           0x08001731   Thumb Code     0  core.o(CODE)
    OS_CPU_SR_Restore                        0x08001739   Thumb Code     0  core.o(CODE)
    OSCtxSw                                  0x0800173f   Thumb Code     0  core.o(CODE)
    OSStartHighRdy                           0x08001749   Thumb Code     0  core.o(CODE)
    PendSV_Handler                           0x08001769   Thumb Code     0  core.o(CODE)
    ADS1256ReadData                          0x080017ad   Thumb Code   106  drive_ads1256.o(i.ADS1256ReadData)
    ADS1256WREG                              0x0800181d   Thumb Code    74  drive_ads1256.o(i.ADS1256WREG)
    ADS1256_Init                             0x0800186d   Thumb Code   154  drive_ads1256.o(i.ADS1256_Init)
    BusFault_Handler                         0x0800190d   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    Change_Menu                              0x08001911   Thumb Code    94  user.o(i.Change_Menu)
    DDSDataInit                              0x08001981   Thumb Code   154  drive_communication.o(i.DDSDataInit)
    DMA2_Stream0_IRQHandler                  0x08001a35   Thumb Code    26  user_adc.o(i.DMA2_Stream0_IRQHandler)
    DMA_ClearFlag                            0x08001a61   Thumb Code    32  stm32f4xx_dma.o(i.DMA_ClearFlag)
    DebugMon_Handler                         0x08001a8d   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Disp_Main                                0x08001a91   Thumb Code   244  user.o(i.Disp_Main)
    FSMC_NORSRAMCmd                          0x08001b8d   Thumb Code    30  stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd)
    FSMC_NORSRAMInit                         0x08001bb1   Thumb Code   202  stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit)
    GPIO_Change_Init                         0x08001c7d   Thumb Code    46  user.o(i.GPIO_Change_Init)
    GPIO_Init                                0x08001cb1   Thumb Code   124  stm32f4xx_gpio.o(i.GPIO_Init)
    GPIO_PinAFConfig                         0x08001d2d   Thumb Code    32  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    GPIO_ReadInputDataBit                    0x08001d4d   Thumb Code    14  stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x08001d5b   Thumb Code     4  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08001d5f   Thumb Code     4  stm32f4xx_gpio.o(i.GPIO_SetBits)
    Get_Val                                  0x08001d65   Thumb Code    48  drive_ads1256.o(i.Get_Val)
    HardFault_Handler                        0x08001d9d   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    Init_ADS1256_GPIO                        0x08001da1   Thumb Code    90  drive_ads1256.o(i.Init_ADS1256_GPIO)
    Init_All                                 0x08001e01   Thumb Code    32  user.o(i.Init_All)
    Init_Uart                                0x08001e21   Thumb Code   126  drive_communication.o(i.Init_Uart)
    Key_StateSweep                           0x08001ea9   Thumb Code   166  drive_ps2.o(i.Key_StateSweep)
    LCD_Appoint_Clear                        0x08001f4f   Thumb Code    72  tft_lcd.o(i.LCD_Appoint_Clear)
    LCD_Clear                                0x08001f99   Thumb Code    32  tft_lcd.o(i.LCD_Clear)
    LCD_CtrlLinesConfig                      0x08001fbd   Thumb Code   326  tft_lcd.o(i.LCD_CtrlLinesConfig)
    LCD_DrawRectS                            0x08002115   Thumb Code    48  tft_lcd.o(i.LCD_DrawRectS)
    LCD_DrawuniLine                          0x08002149   Thumb Code   184  tft_lcd.o(i.LCD_DrawuniLine)
    LCD_FSMCConfig                           0x08002201   Thumb Code    86  tft_lcd.o(i.LCD_FSMCConfig)
    LCD_SetCursor                            0x08002257   Thumb Code    46  tft_lcd.o(i.LCD_SetCursor)
    LCD_SetDisplayWindow                     0x08002285   Thumb Code    48  tft_lcd.o(i.LCD_SetDisplayWindow)
    LCD_WriteRAM                             0x080022b5   Thumb Code     8  tft_lcd.o(i.LCD_WriteRAM)
    LCD_WriteRAM_Prepare                     0x080022bd   Thumb Code     8  tft_lcd.o(i.LCD_WriteRAM_Prepare)
    LED_Control                              0x080022c5   Thumb Code   106  app_led.o(i.LED_Control)
    LED_Init                                 0x08002339   Thumb Code    70  drive_gpio.o(i.LED_Init)
    LED_main                                 0x08002389   Thumb Code    26  app_led.o(i.LED_main)
    Line_fitRead                             0x080023a9   Thumb Code    60  user.o(i.Line_fitRead)
    Line_fitWrite                            0x080023e9   Thumb Code    60  user.o(i.Line_fitWrite)
    MemManage_Handler                        0x08002429   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    MenuHaddler_1                            0x0800242d   Thumb Code   286  user.o(i.MenuHaddler_1)
    MenuHaddler_2                            0x080025cd   Thumb Code   446  user.o(i.MenuHaddler_2)
    MenuHaddler_3                            0x0800286d   Thumb Code    16  user.o(i.MenuHaddler_3)
    MenuHaddler_4                            0x08002881   Thumb Code    16  user.o(i.MenuHaddler_4)
    Moving_Average_Filter                    0x08002895   Thumb Code    88  drive_ads1256.o(i.Moving_Average_Filter)
    MyPs2KeyScan                             0x080028ed   Thumb Code   380  drive_ps2.o(i.MyPs2KeyScan)
    NMI_Handler                              0x08002a71   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    OSDelPrioRdy                             0x08002a75   Thumb Code    14  os_cpu.o(i.OSDelPrioRdy)
    OSGetHighRdy                             0x08002a89   Thumb Code    32  os_cpu.o(i.OSGetHighRdy)
    OSSetPrioRdy                             0x08002aad   Thumb Code    14  os_cpu.o(i.OSSetPrioRdy)
    OSTimeDly                                0x08002ac1   Thumb Code    52  os_cpu.o(i.OSTimeDly)
    OS_Char_Show                             0x08002afd   Thumb Code   196  os_ui.o(i.OS_Char_Show)
    OS_Font_Show                             0x08002bd5   Thumb Code   168  os_ui.o(i.OS_Font_Show)
    OS_HzMat_Get                             0x08002c85   Thumb Code   150  os_ui.o(i.OS_HzMat_Get)
    OS_IDLE_Task                             0x08002d21   Thumb Code     2  os_cpu.o(i.OS_IDLE_Task)
    OS_Init                                  0x08002d23   Thumb Code    22  main.o(i.OS_Init)
    OS_LCD_Init                              0x08002d39   Thumb Code    56  os_ui.o(i.OS_LCD_Init)
    OS_Line_Draw                             0x08002d89   Thumb Code   170  os_ui.o(i.OS_Line_Draw)
    OS_Num_Show                              0x08002e35   Thumb Code    92  os_ui.o(i.OS_Num_Show)
    OS_Point_Draw                            0x08002e95   Thumb Code    22  os_ui.o(i.OS_Point_Draw)
    OS_Rect_Draw                             0x08002ead   Thumb Code   132  os_ui.o(i.OS_Rect_Draw)
    OS_Sched                                 0x08002f35   Thumb Code    58  os_cpu.o(i.OS_Sched)
    OS_SchedLock                             0x08002f79   Thumb Code    20  os_cpu.o(i.OS_SchedLock)
    OS_SchedUnlock                           0x08002f91   Thumb Code    20  os_cpu.o(i.OS_SchedUnlock)
    OS_Start                                 0x08002fa9   Thumb Code    62  os_cpu.o(i.OS_Start)
    OS_String_Show                           0x08002ff9   Thumb Code   110  os_ui.o(i.OS_String_Show)
    PS2_Keyboard_Init                        0x08003099   Thumb Code     4  drive_ps2.o(i.PS2_Keyboard_Init)
    PS2_ReadKeyCodon                         0x0800309d   Thumb Code   168  drive_ps2.o(i.PS2_ReadKeyCodon)
    PS2_ReadNum                              0x08003149   Thumb Code   300  user.o(i.PS2_ReadNum)
    PutPixel                                 0x08003361   Thumb Code    24  tft_lcd.o(i.PutPixel)
    RCC_AHB1PeriphClockCmd                   0x0800337d   Thumb Code    22  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    RCC_AHB3PeriphClockCmd                   0x08003399   Thumb Code    22  stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x080033b5   Thumb Code    22  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_APB2PeriphResetCmd                   0x080033d1   Thumb Code    22  stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd)
    RCC_GetClocksFreq                        0x080033ed   Thumb Code   148  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    SPI1_Init                                0x0800348d   Thumb Code   202  spi.o(i.SPI1_Init)
    SPI1_ReadWriteByte                       0x08003565   Thumb Code    48  spi.o(i.SPI1_ReadWriteByte)
    SPI1_SetSpeed                            0x08003599   Thumb Code    24  spi.o(i.SPI1_SetSpeed)
    SPI_Cmd                                  0x080035b5   Thumb Code    24  stm32f4xx_spi.o(i.SPI_Cmd)
    SPI_I2S_GetFlagStatus                    0x080035cd   Thumb Code    14  stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus)
    SPI_I2S_ReceiveData                      0x080035db   Thumb Code     4  stm32f4xx_spi.o(i.SPI_I2S_ReceiveData)
    SPI_I2S_SendData                         0x080035df   Thumb Code     4  stm32f4xx_spi.o(i.SPI_I2S_SendData)
    SPI_Init                                 0x080035e3   Thumb Code    56  stm32f4xx_spi.o(i.SPI_Init)
    SPI_ReadByte                             0x0800361d   Thumb Code    70  drive_ads1256.o(i.SPI_ReadByte)
    SPI_WriteByte                            0x08003669   Thumb Code    78  drive_ads1256.o(i.SPI_WriteByte)
    SVC_Handler                              0x080036bd   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    Show_Val                                 0x08003779   Thumb Code   110  user.o(i.Show_Val)
    SysTick_CLKSourceConfig                  0x080037f1   Thumb Code    28  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x0800380d   Thumb Code    82  os_cpu.o(i.SysTick_Handler)
    SystemInit                               0x08003869   Thumb Code    74  system_stm32f4xx.o(i.SystemInit)
    System_init                              0x080038c5   Thumb Code    62  os_cpu.o(i.System_init)
    TFT_LCD_Init                             0x08003911   Thumb Code   350  tft_lcd.o(i.TFT_LCD_Init)
    TIM_Cmd                                  0x08003a6f   Thumb Code    24  stm32f4xx_tim.o(i.TIM_Cmd)
    Task_Create                              0x08003a89   Thumb Code   126  os_cpu.o(i.Task_Create)
    Task_End                                 0x08003b11   Thumb Code     2  os_cpu.o(i.Task_End)
    USART1_IRQHandler                        0x08003b15   Thumb Code    92  usart.o(i.USART1_IRQHandler)
    USART_Cmd                                0x08003b7d   Thumb Code    24  stm32f4xx_usart.o(i.USART_Cmd)
    USART_GetFlagStatus                      0x08003b95   Thumb Code    14  stm32f4xx_usart.o(i.USART_GetFlagStatus)
    USART_GetITStatus                        0x08003ba3   Thumb Code    64  stm32f4xx_usart.o(i.USART_GetITStatus)
    USART_Init                               0x08003be5   Thumb Code   180  stm32f4xx_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08003ca1   Thumb Code     8  stm32f4xx_usart.o(i.USART_ReceiveData)
    USART_SendData                           0x08003ca9   Thumb Code     8  stm32f4xx_usart.o(i.USART_SendData)
    UsageFault_Handler                       0x08003cb1   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    User_main                                0x08003cb5   Thumb Code    74  user.o(i.User_main)
    W25Q64_Erase_Sector                      0x08003d09   Thumb Code    70  w25q64.o(i.W25Q64_Erase_Sector)
    W25Q64_Init                              0x08003d55   Thumb Code   106  w25q64.o(i.W25Q64_Init)
    W25Q64_Read                              0x08003dcd   Thumb Code    86  w25q64.o(i.W25Q64_Read)
    W25Q64_ReadID                            0x08003e29   Thumb Code    78  w25q64.o(i.W25Q64_ReadID)
    W25Q64_ReadSR                            0x08003e7d   Thumb Code    42  w25q64.o(i.W25Q64_ReadSR)
    W25Q64_Wait_Busy                         0x08003ead   Thumb Code    12  w25q64.o(i.W25Q64_Wait_Busy)
    W25Q64_Write                             0x08003eb9   Thumb Code   158  w25q64.o(i.W25Q64_Write)
    W25Q64_Write_Enable                      0x08003f5d   Thumb Code    34  w25q64.o(i.W25Q64_Write_Enable)
    W25Q64_Write_NoCheck                     0x08003f85   Thumb Code    62  w25q64.o(i.W25Q64_Write_NoCheck)
    W25Q64_Write_Page                        0x08003fc5   Thumb Code    92  w25q64.o(i.W25Q64_Write_Page)
    __ARM_fpclassify                         0x08004025   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_pow                             0x08004059   Thumb Code  3072  pow.o(i.__hardfp_pow)
    __hardfp_sqrt                            0x08004ca9   Thumb Code   122  sqrt.o(i.__hardfp_sqrt)
    __kernel_poly                            0x08004d23   Thumb Code   248  poly.o(i.__kernel_poly)
    __mathlib_dbl_divzero                    0x08004e21   Thumb Code    28  dunder.o(i.__mathlib_dbl_divzero)
    __mathlib_dbl_infnan2                    0x08004e51   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x08004e69   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_overflow                   0x08004e89   Thumb Code    24  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x08004ea9   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    _is_digit                                0x08004ec9   Thumb Code    14  __printf_wp.o(i._is_digit)
    _sys_exit                                0x08004ed7   Thumb Code     2  usart.o(i._sys_exit)
    calculate_paper_count                    0x08004ed9   Thumb Code   160  user.o(i.calculate_paper_count)
    delay_ms                                 0x08004fb5   Thumb Code    52  delay.o(i.delay_ms)
    delay_us                                 0x08004ff1   Thumb Code    72  delay.o(i.delay_us)
    fabs                                     0x0800503d   Thumb Code    24  fabs.o(i.fabs)
    find_calibrated_interval                 0x08005055   Thumb Code   330  user.o(i.find_calibrated_interval)
    font_init                                0x080051ad   Thumb Code    60  fontupd.o(i.font_init)
    main                                     0x080051ed   Thumb Code    44  main.o(i.main)
    sendData                                 0x08005231   Thumb Code   256  drive_communication.o(i.sendData)
    sqrt                                     0x0800533d   Thumb Code   110  sqrt.o(i.sqrt)
    _get_lc_numeric                          0x080053e1   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __aeabi_dneg                             0x0800540d   Thumb Code     0  basic.o(x$fpl$basic)
    _dneg                                    0x0800540d   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_fneg                             0x08005413   Thumb Code     0  basic.o(x$fpl$basic)
    _fneg                                    0x08005413   Thumb Code     6  basic.o(x$fpl$basic)
    _dabs                                    0x08005419   Thumb Code     6  basic.o(x$fpl$basic)
    _fabs                                    0x0800541f   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_d2f                              0x08005425   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08005425   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x08005489   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08005489   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcheck_NaN1                        0x080055d9   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __fpl_dcmp_Inf                           0x080055e9   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x08005601   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08005601   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_i2d                              0x080058b1   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x080058b1   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_ui2d                             0x080058df   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x080058df   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_cdcmple                          0x08005905   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x08005905   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x08005967   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x0800597d   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x0800597d   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08005ad1   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08005b6d   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x08005b79   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x08005b79   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_drsub                            0x08005be5   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x08005be5   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x08005bfd   Thumb Code   404  dsqrt_umaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x08005d95   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x08005d95   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x08005f69   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08005f69   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x08005fbf   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x0800604b   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08006053   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08006053   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x08006055   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    _printf_fp_dec                           0x0800605f   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x08006063   Thumb Code     4  printf2.o(x$fpl$printf2)
    __fpl_return_NaN                         0x08006067   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x080060cb   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    __fpl_cmpreturn                          0x08006127   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    Font_3216                                0x08006156   Data        6080  fonts.o(.constdata)
    __I$use$fp                               0x08006156   Number         0  usenofp.o(x$fpl$usenofp)
    asc2_1608                                0x08007916   Data        1520  fonts.o(.constdata)
    asc2_2412                                0x08007f06   Data        3420  fonts.o(.constdata)
    asc2_3216                                0x08008c62   Data        6080  fonts.o(.constdata)
    __mathlib_zero                           0x0800a4e8   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x0800a584   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800a5a4   Number         0  anon$$obj.o(Region$$Table)
    mem3base                                 0x10000000   Data       61440  os_malloc.o(.ARM.__AT_0x10000000)
    mem3mapbase                              0x1000f000   Data        3840  os_malloc.o(.ARM.__AT_0x1000F000)
    ADC_Sign                                 0x20000001   Data           1  user_adc.o(.data)
    MenuSign                                 0x20000002   Data           1  user.o(.data)
    Sched_flag                               0x20000004   Data           1  os_cpu.o(.data)
    OS_PrioCur                               0x20000005   Data           1  os_cpu.o(.data)
    OS_PrioHighRdy                           0x20000006   Data           1  os_cpu.o(.data)
    OS_Running                               0x20000007   Data           1  os_cpu.o(.data)
    CPU_ExceptStkBase                        0x20000008   Data           4  os_cpu.o(.data)
    p_TCBHightRdy                            0x2000000c   Data           4  os_cpu.o(.data)
    OSRdyTbl                                 0x20000010   Data           4  os_cpu.o(.data)
    fac_ms                                   0x20000014   Data           4  os_cpu.o(.data)
    fac_us                                   0x20000018   Data           4  os_cpu.o(.data)
    p_TCB_Cur                                0x2000001c   Data           4  os_cpu.o(.data)
    USART_RX_STA                             0x20000020   Data           2  usart.o(.data)
    Ps2KeyValue                              0x20000024   Data           1  drive_ps2.o(.data)
    Count                                    0x20000028   Data           4  drive_ps2.o(.data)
    Font32x16                                0x2000002c   Data           8  fonts.o(.data)
    TextColor                                0x20000034   Data           2  tft_lcd.o(.data)
    BackColor                                0x20000036   Data           2  tft_lcd.o(.data)
    W25Q64_TYPE                              0x2000003c   Data           2  w25q64.o(.data)
    SystemCoreClock                          0x20000050   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x20000054   Data          16  system_stm32f4xx.o(.data)
    A                                        0x20000064   Data         400  user.o(.bss)
    is_calibrated                            0x200001f4   Data         100  user.o(.bss)
    TASK_0_STK                               0x20000258   Data       36000  main.o(.bss)
    TASK_1_STK                               0x20008ef8   Data        2048  main.o(.bss)
    TASK_5_STK                               0x200096f8   Data         256  main.o(.bss)
    CPU_ExceptStk                            0x200097f8   Data         512  os_cpu.o(.bss)
    IDLE_STK                                 0x200099f8   Data         256  os_cpu.o(.bss)
    TCB_Task                                 0x20009af8   Data         384  os_cpu.o(.bss)
    USART_RX_BUF                             0x20009c78   Data         200  usart.o(.bss)
    Key_FSM_PS2                              0x20009d40   Data          10  drive_ps2.o(.bss)
    communicationData                        0x20009d4c   Data          97  drive_communication.o(.bss)
    dds                                      0x20009db0   Data         456  drive_communication.o(.bss)
    ftinfo                                   0x20009f78   Data          33  fontupd.o(.bss)
    W25Q64_BUFFER                            0x20009f99   Data        4096  w25q64.o(.bss)
    __libspace_start                         0x2000af9c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x2000affc   Data           0  libspace.o(.bss)
    mem2base                                 0x68000000   Data       983040  os_malloc.o(.ARM.__AT_0x68000000)
    mem2mapbase                              0x680f0000   Data       61440  os_malloc.o(.ARM.__AT_0x680F0000)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000a624, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000a5c0, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO         5536    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         5726  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         6239    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         6241    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         6243    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         5719    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         5715    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000202   0x08000202   0x00000006   Code   RO         5716    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000208   0x08000208   0x00000006   Code   RO         5717    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800020e   0x0800020e   0x00000006   Code   RO         5718    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000214   0x08000214   0x00000004   Code   RO         5872    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000218   0x08000218   0x00000002   Code   RO         6059    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800021a   0x0800021a   0x00000004   Code   RO         6060    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         6063    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         6066    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         6068    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         6070    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000006   Code   RO         6071    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x00000000   Code   RO         6073    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x00000000   Code   RO         6075    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x00000000   Code   RO         6077    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x0000000a   Code   RO         6078    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6079    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6081    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6083    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6085    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6087    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6089    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6091    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6093    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6097    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6099    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6101    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         6103    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000002   Code   RO         6104    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000230   0x08000230   0x00000002   Code   RO         6185    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000232   0x08000232   0x00000000   Code   RO         6220    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         6222    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         6224    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         6227    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         6230    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         6232    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         6235    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000002   Code   RO         6236    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         5856    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000234   0x08000234   0x00000000   Code   RO         5967    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000234   0x08000234   0x00000006   Code   RO         5979    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         5969    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800023a   0x0800023a   0x00000004   Code   RO         5970    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO         5972    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800023e   0x0800023e   0x00000008   Code   RO         5973    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000246   0x08000246   0x00000002   Code   RO         6107    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000248   0x08000248   0x00000000   Code   RO         6143    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000248   0x08000248   0x00000004   Code   RO         6144    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800024c   0x0800024c   0x00000006   Code   RO         6145    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000252   0x08000252   0x00000002   PAD
    0x08000254   0x08000254   0x0000003c   Code   RO         5537    .text               startup_stm32f40_41xxx.o
    0x08000290   0x08000290   0x00000002   Code   RO         5683    .text               c_w.l(use_no_semi_2.o)
    0x08000292   0x08000292   0x00000002   PAD
    0x08000294   0x08000294   0x00000028   Code   RO         5689    .text               c_w.l(noretval__2sprintf.o)
    0x080002bc   0x080002bc   0x0000004e   Code   RO         5693    .text               c_w.l(_printf_pad.o)
    0x0800030a   0x0800030a   0x00000002   PAD
    0x0800030c   0x0800030c   0x00000188   Code   RO         5712    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000494   0x08000494   0x00000064   Code   RO         5720    .text               c_w.l(rt_memcpy_w.o)
    0x080004f8   0x080004f8   0x0000004e   Code   RO         5722    .text               c_w.l(rt_memclr_w.o)
    0x08000546   0x08000546   0x00000006   Code   RO         5724    .text               c_w.l(heapauxi.o)
    0x0800054c   0x0800054c   0x00000002   Code   RO         5854    .text               c_w.l(use_no_semi.o)
    0x0800054e   0x0800054e   0x00000016   Code   RO         5861    .text               c_w.l(_rserrno.o)
    0x08000564   0x08000564   0x0000041e   Code   RO         5863    .text               c_w.l(_printf_fp_dec.o)
    0x08000982   0x08000982   0x00000002   PAD
    0x08000984   0x08000984   0x000002fc   Code   RO         5865    .text               c_w.l(_printf_fp_hex.o)
    0x08000c80   0x08000c80   0x00000030   Code   RO         5868    .text               c_w.l(_printf_char_common.o)
    0x08000cb0   0x08000cb0   0x0000000a   Code   RO         5870    .text               c_w.l(_sputc.o)
    0x08000cba   0x08000cba   0x00000002   PAD
    0x08000cbc   0x08000cbc   0x00000008   Code   RO         5986    .text               c_w.l(rt_locale_intlibspace.o)
    0x08000cc4   0x08000cc4   0x00000008   Code   RO         5991    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000ccc   0x08000ccc   0x0000008a   Code   RO         5993    .text               c_w.l(lludiv10.o)
    0x08000d56   0x08000d56   0x00000002   PAD
    0x08000d58   0x08000d58   0x00000080   Code   RO         5997    .text               c_w.l(_printf_fp_infnan.o)
    0x08000dd8   0x08000dd8   0x000000e4   Code   RO         6003    .text               c_w.l(bigflt0.o)
    0x08000ebc   0x08000ebc   0x00000008   Code   RO         6037    .text               c_w.l(libspace.o)
    0x08000ec4   0x08000ec4   0x0000004a   Code   RO         6040    .text               c_w.l(sys_stackheap_outer.o)
    0x08000f0e   0x08000f0e   0x00000012   Code   RO         6046    .text               c_w.l(exit.o)
    0x08000f20   0x08000f20   0x00000080   Code   RO         6052    .text               c_w.l(strcmpv7m.o)
    0x08000fa0   0x08000fa0   0x0000003e   Code   RO         6006    CL$$btod_d2e        c_w.l(btod.o)
    0x08000fde   0x08000fde   0x00000046   Code   RO         6008    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08001024   0x08001024   0x00000060   Code   RO         6007    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08001084   0x08001084   0x00000338   Code   RO         6016    CL$$btod_div_common  c_w.l(btod.o)
    0x080013bc   0x080013bc   0x000000dc   Code   RO         6013    CL$$btod_e2e        c_w.l(btod.o)
    0x08001498   0x08001498   0x0000002a   Code   RO         6010    CL$$btod_ediv       c_w.l(btod.o)
    0x080014c2   0x080014c2   0x0000002a   Code   RO         6009    CL$$btod_emul       c_w.l(btod.o)
    0x080014ec   0x080014ec   0x00000244   Code   RO         6015    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001730   0x08001730   0x0000007c   Code   RO          940    CODE                core.o
    0x080017ac   0x080017ac   0x00000070   Code   RO         1704    i.ADS1256ReadData   drive_ads1256.o
    0x0800181c   0x0800181c   0x00000050   Code   RO         1705    i.ADS1256WREG       drive_ads1256.o
    0x0800186c   0x0800186c   0x000000a0   Code   RO         1706    i.ADS1256_Init      drive_ads1256.o
    0x0800190c   0x0800190c   0x00000002   Code   RO         5414    i.BusFault_Handler  stm32f4xx_it.o
    0x0800190e   0x0800190e   0x00000002   PAD
    0x08001910   0x08001910   0x00000070   Code   RO          387    i.Change_Menu       user.o
    0x08001980   0x08001980   0x000000b4   Code   RO         1655    i.DDSDataInit       drive_communication.o
    0x08001a34   0x08001a34   0x0000002c   Code   RO          303    i.DMA2_Stream0_IRQHandler  user_adc.o
    0x08001a60   0x08001a60   0x0000002c   Code   RO         3006    i.DMA_ClearFlag     stm32f4xx_dma.o
    0x08001a8c   0x08001a8c   0x00000002   Code   RO         5415    i.DebugMon_Handler  stm32f4xx_it.o
    0x08001a8e   0x08001a8e   0x00000002   PAD
    0x08001a90   0x08001a90   0x000000fc   Code   RO          388    i.Disp_Main         user.o
    0x08001b8c   0x08001b8c   0x00000024   Code   RO         3461    i.FSMC_NORSRAMCmd   stm32f4xx_fsmc.o
    0x08001bb0   0x08001bb0   0x000000ca   Code   RO         3463    i.FSMC_NORSRAMInit  stm32f4xx_fsmc.o
    0x08001c7a   0x08001c7a   0x00000002   PAD
    0x08001c7c   0x08001c7c   0x00000034   Code   RO          389    i.GPIO_Change_Init  user.o
    0x08001cb0   0x08001cb0   0x0000007c   Code   RO         3582    i.GPIO_Init         stm32f4xx_gpio.o
    0x08001d2c   0x08001d2c   0x00000020   Code   RO         3583    i.GPIO_PinAFConfig  stm32f4xx_gpio.o
    0x08001d4c   0x08001d4c   0x0000000e   Code   RO         3586    i.GPIO_ReadInputDataBit  stm32f4xx_gpio.o
    0x08001d5a   0x08001d5a   0x00000004   Code   RO         3589    i.GPIO_ResetBits    stm32f4xx_gpio.o
    0x08001d5e   0x08001d5e   0x00000004   Code   RO         3590    i.GPIO_SetBits      stm32f4xx_gpio.o
    0x08001d62   0x08001d62   0x00000002   PAD
    0x08001d64   0x08001d64   0x00000038   Code   RO         1707    i.Get_Val           drive_ads1256.o
    0x08001d9c   0x08001d9c   0x00000002   Code   RO         5416    i.HardFault_Handler  stm32f4xx_it.o
    0x08001d9e   0x08001d9e   0x00000002   PAD
    0x08001da0   0x08001da0   0x00000060   Code   RO         1708    i.Init_ADS1256_GPIO  drive_ads1256.o
    0x08001e00   0x08001e00   0x00000020   Code   RO          391    i.Init_All          user.o
    0x08001e20   0x08001e20   0x00000088   Code   RO         1656    i.Init_Uart         drive_communication.o
    0x08001ea8   0x08001ea8   0x000000a6   Code   RO         1461    i.Key_StateSweep    drive_ps2.o
    0x08001f4e   0x08001f4e   0x00000048   Code   RO         2242    i.LCD_Appoint_Clear  tft_lcd.o
    0x08001f96   0x08001f96   0x00000002   PAD
    0x08001f98   0x08001f98   0x00000024   Code   RO         2243    i.LCD_Clear         tft_lcd.o
    0x08001fbc   0x08001fbc   0x00000158   Code   RO         2244    i.LCD_CtrlLinesConfig  tft_lcd.o
    0x08002114   0x08002114   0x00000034   Code   RO         2257    i.LCD_DrawRectS     tft_lcd.o
    0x08002148   0x08002148   0x000000b8   Code   RO         2258    i.LCD_DrawuniLine   tft_lcd.o
    0x08002200   0x08002200   0x00000056   Code   RO         2259    i.LCD_FSMCConfig    tft_lcd.o
    0x08002256   0x08002256   0x0000002e   Code   RO         2265    i.LCD_SetCursor     tft_lcd.o
    0x08002284   0x08002284   0x00000030   Code   RO         2266    i.LCD_SetDisplayWindow  tft_lcd.o
    0x080022b4   0x080022b4   0x00000008   Code   RO         2455    i.LCD_WriteRAM      tft_lcd.o
    0x080022bc   0x080022bc   0x00000008   Code   RO         2465    i.LCD_WriteRAM_Prepare  tft_lcd.o
    0x080022c4   0x080022c4   0x00000074   Code   RO          264    i.LED_Control       app_led.o
    0x08002338   0x08002338   0x00000050   Code   RO         1066    i.LED_Init          drive_gpio.o
    0x08002388   0x08002388   0x00000020   Code   RO          265    i.LED_main          app_led.o
    0x080023a8   0x080023a8   0x00000040   Code   RO          392    i.Line_fitRead      user.o
    0x080023e8   0x080023e8   0x00000040   Code   RO          393    i.Line_fitWrite     user.o
    0x08002428   0x08002428   0x00000002   Code   RO         5417    i.MemManage_Handler  stm32f4xx_it.o
    0x0800242a   0x0800242a   0x00000002   PAD
    0x0800242c   0x0800242c   0x000001a0   Code   RO          394    i.MenuHaddler_1     user.o
    0x080025cc   0x080025cc   0x000002a0   Code   RO          395    i.MenuHaddler_2     user.o
    0x0800286c   0x0800286c   0x00000014   Code   RO          396    i.MenuHaddler_3     user.o
    0x08002880   0x08002880   0x00000014   Code   RO          397    i.MenuHaddler_4     user.o
    0x08002894   0x08002894   0x00000058   Code   RO         1709    i.Moving_Average_Filter  drive_ads1256.o
    0x080028ec   0x080028ec   0x00000184   Code   RO         1462    i.MyPs2KeyScan      drive_ps2.o
    0x08002a70   0x08002a70   0x00000002   Code   RO         5418    i.NMI_Handler       stm32f4xx_it.o
    0x08002a72   0x08002a72   0x00000002   PAD
    0x08002a74   0x08002a74   0x00000014   Code   RO          677    i.OSDelPrioRdy      os_cpu.o
    0x08002a88   0x08002a88   0x00000024   Code   RO          580    i.OSGetHighRdy      os_cpu.o
    0x08002aac   0x08002aac   0x00000014   Code   RO          672    i.OSSetPrioRdy      os_cpu.o
    0x08002ac0   0x08002ac0   0x0000003c   Code   RO          583    i.OSTimeDly         os_cpu.o
    0x08002afc   0x08002afc   0x000000d8   Code   RO          727    i.OS_Char_Show      os_ui.o
    0x08002bd4   0x08002bd4   0x000000b0   Code   RO          729    i.OS_Font_Show      os_ui.o
    0x08002c84   0x08002c84   0x0000009c   Code   RO          730    i.OS_HzMat_Get      os_ui.o
    0x08002d20   0x08002d20   0x00000002   Code   RO          584    i.OS_IDLE_Task      os_cpu.o
    0x08002d22   0x08002d22   0x00000016   Code   RO          551    i.OS_Init           main.o
    0x08002d38   0x08002d38   0x00000050   Code   RO          732    i.OS_LCD_Init       os_ui.o
    0x08002d88   0x08002d88   0x000000aa   Code   RO          733    i.OS_Line_Draw      os_ui.o
    0x08002e32   0x08002e32   0x00000002   PAD
    0x08002e34   0x08002e34   0x00000060   Code   RO          734    i.OS_Num_Show       os_ui.o
    0x08002e94   0x08002e94   0x00000016   Code   RO          736    i.OS_Point_Draw     os_ui.o
    0x08002eaa   0x08002eaa   0x00000002   PAD
    0x08002eac   0x08002eac   0x00000088   Code   RO          737    i.OS_Rect_Draw      os_ui.o
    0x08002f34   0x08002f34   0x00000044   Code   RO          585    i.OS_Sched          os_cpu.o
    0x08002f78   0x08002f78   0x00000018   Code   RO          586    i.OS_SchedLock      os_cpu.o
    0x08002f90   0x08002f90   0x00000018   Code   RO          587    i.OS_SchedUnlock    os_cpu.o
    0x08002fa8   0x08002fa8   0x00000050   Code   RO          588    i.OS_Start          os_cpu.o
    0x08002ff8   0x08002ff8   0x0000006e   Code   RO          738    i.OS_String_Show    os_ui.o
    0x08003066   0x08003066   0x00000002   PAD
    0x08003068   0x08003068   0x00000030   Code   RO         1463    i.PS2_GPIO_Init     drive_ps2.o
    0x08003098   0x08003098   0x00000004   Code   RO         1464    i.PS2_Keyboard_Init  drive_ps2.o
    0x0800309c   0x0800309c   0x000000ac   Code   RO         1465    i.PS2_ReadKeyCodon  drive_ps2.o
    0x08003148   0x08003148   0x00000148   Code   RO          398    i.PS2_ReadNum       user.o
    0x08003290   0x08003290   0x0000005c   Code   RO         1466    i.PS2_SCL_Set       drive_ps2.o
    0x080032ec   0x080032ec   0x00000074   Code   RO         1467    i.PS2_SCL_Wait      drive_ps2.o
    0x08003360   0x08003360   0x0000001c   Code   RO         2272    i.PutPixel          tft_lcd.o
    0x0800337c   0x0800337c   0x0000001c   Code   RO         3903    i.RCC_AHB1PeriphClockCmd  stm32f4xx_rcc.o
    0x08003398   0x08003398   0x0000001c   Code   RO         3909    i.RCC_AHB3PeriphClockCmd  stm32f4xx_rcc.o
    0x080033b4   0x080033b4   0x0000001c   Code   RO         3915    i.RCC_APB2PeriphClockCmd  stm32f4xx_rcc.o
    0x080033d0   0x080033d0   0x0000001c   Code   RO         3917    i.RCC_APB2PeriphResetCmd  stm32f4xx_rcc.o
    0x080033ec   0x080033ec   0x000000a0   Code   RO         3924    i.RCC_GetClocksFreq  stm32f4xx_rcc.o
    0x0800348c   0x0800348c   0x000000d8   Code   RO         2173    i.SPI1_Init         spi.o
    0x08003564   0x08003564   0x00000034   Code   RO         2174    i.SPI1_ReadWriteByte  spi.o
    0x08003598   0x08003598   0x0000001c   Code   RO         2175    i.SPI1_SetSpeed     spi.o
    0x080035b4   0x080035b4   0x00000018   Code   RO         5037    i.SPI_Cmd           stm32f4xx_spi.o
    0x080035cc   0x080035cc   0x0000000e   Code   RO         5045    i.SPI_I2S_GetFlagStatus  stm32f4xx_spi.o
    0x080035da   0x080035da   0x00000004   Code   RO         5048    i.SPI_I2S_ReceiveData  stm32f4xx_spi.o
    0x080035de   0x080035de   0x00000004   Code   RO         5049    i.SPI_I2S_SendData  stm32f4xx_spi.o
    0x080035e2   0x080035e2   0x00000038   Code   RO         5050    i.SPI_Init          stm32f4xx_spi.o
    0x0800361a   0x0800361a   0x00000002   PAD
    0x0800361c   0x0800361c   0x0000004c   Code   RO         1710    i.SPI_ReadByte      drive_ads1256.o
    0x08003668   0x08003668   0x00000054   Code   RO         1711    i.SPI_WriteByte     drive_ads1256.o
    0x080036bc   0x080036bc   0x00000002   Code   RO         5419    i.SVC_Handler       stm32f4xx_it.o
    0x080036be   0x080036be   0x00000002   PAD
    0x080036c0   0x080036c0   0x000000b8   Code   RO         5499    i.SetSysClock       system_stm32f4xx.o
    0x08003778   0x08003778   0x00000078   Code   RO          399    i.Show_Val          user.o
    0x080037f0   0x080037f0   0x0000001c   Code   RO         4991    i.SysTick_CLKSourceConfig  misc.o
    0x0800380c   0x0800380c   0x0000005c   Code   RO          589    i.SysTick_Handler   os_cpu.o
    0x08003868   0x08003868   0x0000005c   Code   RO         5501    i.SystemInit        system_stm32f4xx.o
    0x080038c4   0x080038c4   0x0000004c   Code   RO          590    i.System_init       os_cpu.o
    0x08003910   0x08003910   0x0000015e   Code   RO         2274    i.TFT_LCD_Init      tft_lcd.o
    0x08003a6e   0x08003a6e   0x00000018   Code   RO         4253    i.TIM_Cmd           stm32f4xx_tim.o
    0x08003a86   0x08003a86   0x00000002   PAD
    0x08003a88   0x08003a88   0x00000088   Code   RO          591    i.Task_Create       os_cpu.o
    0x08003b10   0x08003b10   0x00000002   Code   RO          592    i.Task_End          os_cpu.o
    0x08003b12   0x08003b12   0x00000002   PAD
    0x08003b14   0x08003b14   0x00000068   Code   RO         1013    i.USART1_IRQHandler  usart.o
    0x08003b7c   0x08003b7c   0x00000018   Code   RO         4803    i.USART_Cmd         stm32f4xx_usart.o
    0x08003b94   0x08003b94   0x0000000e   Code   RO         4806    i.USART_GetFlagStatus  stm32f4xx_usart.o
    0x08003ba2   0x08003ba2   0x00000040   Code   RO         4807    i.USART_GetITStatus  stm32f4xx_usart.o
    0x08003be2   0x08003be2   0x00000002   PAD
    0x08003be4   0x08003be4   0x000000bc   Code   RO         4810    i.USART_Init        stm32f4xx_usart.o
    0x08003ca0   0x08003ca0   0x00000008   Code   RO         4817    i.USART_ReceiveData  stm32f4xx_usart.o
    0x08003ca8   0x08003ca8   0x00000008   Code   RO         4820    i.USART_SendData    stm32f4xx_usart.o
    0x08003cb0   0x08003cb0   0x00000002   Code   RO         5420    i.UsageFault_Handler  stm32f4xx_it.o
    0x08003cb2   0x08003cb2   0x00000002   PAD
    0x08003cb4   0x08003cb4   0x00000054   Code   RO          403    i.User_main         user.o
    0x08003d08   0x08003d08   0x0000004c   Code   RO         2488    i.W25Q64_Erase_Sector  w25q64.o
    0x08003d54   0x08003d54   0x00000078   Code   RO         2489    i.W25Q64_Init       w25q64.o
    0x08003dcc   0x08003dcc   0x0000005c   Code   RO         2491    i.W25Q64_Read       w25q64.o
    0x08003e28   0x08003e28   0x00000054   Code   RO         2492    i.W25Q64_ReadID     w25q64.o
    0x08003e7c   0x08003e7c   0x00000030   Code   RO         2493    i.W25Q64_ReadSR     w25q64.o
    0x08003eac   0x08003eac   0x0000000c   Code   RO         2495    i.W25Q64_Wait_Busy  w25q64.o
    0x08003eb8   0x08003eb8   0x000000a4   Code   RO         2496    i.W25Q64_Write      w25q64.o
    0x08003f5c   0x08003f5c   0x00000028   Code   RO         2498    i.W25Q64_Write_Enable  w25q64.o
    0x08003f84   0x08003f84   0x0000003e   Code   RO         2499    i.W25Q64_Write_NoCheck  w25q64.o
    0x08003fc2   0x08003fc2   0x00000002   PAD
    0x08003fc4   0x08003fc4   0x00000060   Code   RO         2500    i.W25Q64_Write_Page  w25q64.o
    0x08004024   0x08004024   0x00000030   Code   RO         5931    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08004054   0x08004054   0x00000004   PAD
    0x08004058   0x08004058   0x00000c50   Code   RO         5804    i.__hardfp_pow      m_wm.l(pow.o)
    0x08004ca8   0x08004ca8   0x0000007a   Code   RO         5842    i.__hardfp_sqrt     m_wm.l(sqrt.o)
    0x08004d22   0x08004d22   0x000000f8   Code   RO         5949    i.__kernel_poly     m_wm.l(poly.o)
    0x08004e1a   0x08004e1a   0x00000006   PAD
    0x08004e20   0x08004e20   0x00000030   Code   RO         5911    i.__mathlib_dbl_divzero  m_wm.l(dunder.o)
    0x08004e50   0x08004e50   0x00000014   Code   RO         5913    i.__mathlib_dbl_infnan2  m_wm.l(dunder.o)
    0x08004e64   0x08004e64   0x00000004   PAD
    0x08004e68   0x08004e68   0x00000020   Code   RO         5914    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x08004e88   0x08004e88   0x00000020   Code   RO         5915    i.__mathlib_dbl_overflow  m_wm.l(dunder.o)
    0x08004ea8   0x08004ea8   0x00000020   Code   RO         5917    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x08004ec8   0x08004ec8   0x0000000e   Code   RO         5705    i._is_digit         c_w.l(__printf_wp.o)
    0x08004ed6   0x08004ed6   0x00000002   Code   RO         1014    i._sys_exit         usart.o
    0x08004ed8   0x08004ed8   0x000000a8   Code   RO          404    i.calculate_paper_count  user.o
    0x08004f80   0x08004f80   0x00000034   Code   RO         1657    i.crc_16            drive_communication.o
    0x08004fb4   0x08004fb4   0x0000003c   Code   RO          984    i.delay_ms          delay.o
    0x08004ff0   0x08004ff0   0x0000004c   Code   RO          985    i.delay_us          delay.o
    0x0800503c   0x0800503c   0x00000018   Code   RO         5927    i.fabs              m_wm.l(fabs.o)
    0x08005054   0x08005054   0x00000158   Code   RO          405    i.find_calibrated_interval  user.o
    0x080051ac   0x080051ac   0x00000040   Code   RO         2137    i.font_init         fontupd.o
    0x080051ec   0x080051ec   0x00000044   Code   RO          552    i.main              main.o
    0x08005230   0x08005230   0x0000010c   Code   RO         1658    i.sendData          drive_communication.o
    0x0800533c   0x0800533c   0x0000006e   Code   RO         5844    i.sqrt              m_wm.l(sqrt.o)
    0x080053aa   0x080053aa   0x00000002   PAD
    0x080053ac   0x080053ac   0x00000034   Code   RO         1659    i.usartSendData     drive_communication.o
    0x080053e0   0x080053e0   0x0000002c   Code   RO         6029    locale$$code        c_w.l(lc_numeric_c.o)
    0x0800540c   0x0800540c   0x00000018   Code   RO         5728    x$fpl$basic         fz_wm.l(basic.o)
    0x08005424   0x08005424   0x00000062   Code   RO         5730    x$fpl$d2f           fz_wm.l(d2f.o)
    0x08005486   0x08005486   0x00000002   PAD
    0x08005488   0x08005488   0x00000150   Code   RO         5732    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x080055d8   0x080055d8   0x00000010   Code   RO         6031    x$fpl$dcheck1       fz_wm.l(dcheck1.o)
    0x080055e8   0x080055e8   0x00000018   Code   RO         5875    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x08005600   0x08005600   0x000002b0   Code   RO         5739    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x080058b0   0x080058b0   0x0000002e   Code   RO         5755    x$fpl$dflt          fz_wm.l(dflt_clz.o)
    0x080058de   0x080058de   0x00000026   Code   RO         5754    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x08005904   0x08005904   0x00000078   Code   RO         5760    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x0800597c   0x0800597c   0x00000154   Code   RO         5762    x$fpl$dmul          fz_wm.l(dmul.o)
    0x08005ad0   0x08005ad0   0x0000009c   Code   RO         5879    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x08005b6c   0x08005b6c   0x0000000c   Code   RO         5881    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08005b78   0x08005b78   0x0000006c   Code   RO         5883    x$fpl$drleqf        fz_wm.l(drleqf.o)
    0x08005be4   0x08005be4   0x00000016   Code   RO         5733    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x08005bfa   0x08005bfa   0x00000002   PAD
    0x08005bfc   0x08005bfc   0x00000198   Code   RO         5885    x$fpl$dsqrt         fz_wm.l(dsqrt_umaal.o)
    0x08005d94   0x08005d94   0x000001d4   Code   RO         5734    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x08005f68   0x08005f68   0x00000056   Code   RO         5764    x$fpl$f2d           fz_wm.l(f2d.o)
    0x08005fbe   0x08005fbe   0x0000008c   Code   RO         5887    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x0800604a   0x0800604a   0x0000000a   Code   RO         6124    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08006054   0x08006054   0x0000000a   Code   RO         5889    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x0800605e   0x0800605e   0x00000004   Code   RO         5766    x$fpl$printf1       fz_wm.l(printf1.o)
    0x08006062   0x08006062   0x00000004   Code   RO         5768    x$fpl$printf2       fz_wm.l(printf2.o)
    0x08006066   0x08006066   0x00000064   Code   RO         6105    x$fpl$retnan        fz_wm.l(retnan.o)
    0x080060ca   0x080060ca   0x0000005c   Code   RO         5891    x$fpl$scalbn        fz_wm.l(scalbn.o)
    0x08006126   0x08006126   0x00000030   Code   RO         6132    x$fpl$trapveneer    fz_wm.l(trapv.o)
    0x08006156   0x08006156   0x00000000   Code   RO         5893    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08006156   0x08006156   0x000017c0   Data   RO         2115    .constdata          fonts.o
    0x08007916   0x08007916   0x000005f0   Data   RO         2117    .constdata          fonts.o
    0x08007f06   0x08007f06   0x00000d5c   Data   RO         2118    .constdata          fonts.o
    0x08008c62   0x08008c62   0x000017c0   Data   RO         2119    .constdata          fonts.o
    0x0800a422   0x0800a422   0x00000011   Data   RO         5713    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x0800a433   0x0800a433   0x00000005   PAD
    0x0800a438   0x0800a438   0x00000088   Data   RO         5807    .constdata          m_wm.l(pow.o)
    0x0800a4c0   0x0800a4c0   0x00000026   Data   RO         5866    .constdata          c_w.l(_printf_fp_hex.o)
    0x0800a4e6   0x0800a4e6   0x00000002   PAD
    0x0800a4e8   0x0800a4e8   0x00000008   Data   RO         5951    .constdata          m_wm.l(qnan.o)
    0x0800a4f0   0x0800a4f0   0x00000094   Data   RO         6004    .constdata          c_w.l(bigflt0.o)
    0x0800a584   0x0800a584   0x00000020   Data   RO         6237    Region$$Table       anon$$obj.o
    0x0800a5a4   0x0800a5a4   0x0000001c   Data   RO         6028    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800a5c0, Size: 0x00013000, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800a5c0   0x00000001   Data   RW          266    .data               app_led.o
    0x20000001   0x0800a5c1   0x00000001   Data   RW          310    .data               user_adc.o
    0x20000002   0x0800a5c2   0x00000001   Data   RW          409    .data               user.o
    0x20000003   0x0800a5c3   0x00000001   PAD
    0x20000004   0x0800a5c4   0x00000018   Data   RW          594    .data               os_cpu.o
    0x2000001c   0x0800a5dc   0x00000004   Data   RW          595    .data               os_cpu.o
    0x20000020   0x0800a5e0   0x00000002   Data   RW         1018    .data               usart.o
    0x20000022   0x0800a5e2   0x00000002   PAD
    0x20000024   0x0800a5e4   0x00000008   Data   RW         1469    .data               drive_ps2.o
    0x2000002c   0x0800a5ec   0x00000008   Data   RW         2123    .data               fonts.o
    0x20000034   0x0800a5f4   0x00000008   Data   RW         2275    .data               tft_lcd.o
    0x2000003c   0x0800a5fc   0x00000002   Data   RW         2503    .data               w25q64.o
    0x2000003e   0x0800a5fe   0x00000010   Data   RW         3956    .data               stm32f4xx_rcc.o
    0x2000004e   0x0800a60e   0x00000002   PAD
    0x20000050   0x0800a610   0x00000014   Data   RW         5502    .data               system_stm32f4xx.o
    0x20000064        -       0x000001f4   Zero   RW          408    .bss                user.o
    0x20000258        -       0x000095a0   Zero   RW          553    .bss                main.o
    0x200097f8        -       0x00000480   Zero   RW          593    .bss                os_cpu.o
    0x20009c78        -       0x000000c8   Zero   RW         1017    .bss                usart.o
    0x20009d40        -       0x0000000a   Zero   RW         1468    .bss                drive_ps2.o
    0x20009d4a   0x0800a624   0x00000002   PAD
    0x20009d4c        -       0x0000022c   Zero   RW         1660    .bss                drive_communication.o
    0x20009f78        -       0x00000021   Zero   RW         2138    .bss                fontupd.o
    0x20009f99        -       0x00001000   Zero   RW         2502    .bss                w25q64.o
    0x2000af99   0x0800a624   0x00000003   PAD
    0x2000af9c        -       0x00000060   Zero   RW         6038    .bss                c_w.l(libspace.o)
    0x2000affc   0x0800a624   0x00000004   PAD
    0x2000b000        -       0x00000000   Zero   RW         5535    HEAP                startup_stm32f40_41xxx.o
    0x2000b000        -       0x00008000   Zero   RW         5534    STACK               startup_stm32f40_41xxx.o



  Load Region LR$$.ARM.__AT_0x10000000 (Base: 0x10000000, Size: 0x00000000, Max: 0x0000f000, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x10000000 (Exec base: 0x10000000, Load base: 0x10000000, Size: 0x0000f000, Max: 0x0000f000, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x10000000        -       0x0000f000   Zero   RW          870    .ARM.__AT_0x10000000  os_malloc.o



  Load Region LR$$.ARM.__AT_0x1000F000 (Base: 0x1000f000, Size: 0x00000000, Max: 0x00000f00, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x1000F000 (Exec base: 0x1000f000, Load base: 0x1000f000, Size: 0x00000f00, Max: 0x00000f00, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x1000f000        -       0x00000f00   Zero   RW          871    .ARM.__AT_0x1000F000  os_malloc.o



  Load Region LR$$.ARM.__AT_0x68000000 (Base: 0x68000000, Size: 0x00000000, Max: 0x000f0000, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x68000000 (Exec base: 0x68000000, Load base: 0x68000000, Size: 0x000f0000, Max: 0x000f0000, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x68000000        -       0x000f0000   Zero   RW          872    .ARM.__AT_0x68000000  os_malloc.o



  Load Region LR$$.ARM.__AT_0x680F0000 (Base: 0x680f0000, Size: 0x00000000, Max: 0x0000f000, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x680F0000 (Exec base: 0x680f0000, Load base: 0x680f0000, Size: 0x0000f000, Max: 0x0000f000, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x680f0000        -       0x0000f000   Zero   RW          873    .ARM.__AT_0x680F0000  os_malloc.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       148         16          0          1          0       1013   app_led.o
         0          0          0          0          0      81040   app_touch.o
         0          0          0          0          0        673   character.o
       124         20          0          0          0        236   core.o
       136         12          0          0          0       1270   delay.o
       752         44          0          0          0       4948   drive_ads1256.o
       688         54          0          0        556       4961   drive_communication.o
        80         10          0          0          0        499   drive_gpio.o
       986         30          0          8         10       7695   drive_ps2.o
         0          0      17100          8          0       1306   fonts.o
        64          4          0          0         33       1201   fontupd.o
        90         24          0          0      38304       1380   main.o
        28          0          0          0          0        550   misc.o
       640         94          0         28       1152     235794   os_cpu.o
         0          0          0          0    1109760       1630   os_malloc.o
      1162         66          0          0          0       9553   os_ui.o
       296         22          0          0          0       1665   spi.o
        60         22        392          0      32768        804   startup_stm32f40_41xxx.o
        44         12          0          0          0       1808   stm32f4xx_dma.o
       238          6          0          0          0       1885   stm32f4xx_fsmc.o
       178          0          0          0          0       3507   stm32f4xx_gpio.o
        14          0          0          0          0       2967   stm32f4xx_it.o
       272         36          0         16          0       5372   stm32f4xx_rcc.o
       102          0          0          0          0       3514   stm32f4xx_spi.o
        24          0          0          0          0       1126   stm32f4xx_tim.o
       306          8          0          0          0       4884   stm32f4xx_usart.o
       276         34          0         20          0       1649   system_stm32f4xx.o
      1262         30          0          8          0      14279   tft_lcd.o
       106         12          0          2        200       2776   usart.o
      2748        480          0          1        500      11218   user.o
        44         18          0          1          0        949   user_adc.o
       794         54          0          2       4096       6785   w25q64.o

    ----------------------------------------------------------------------
     11698       <USER>      <GROUP>        100    1187384     418937   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        36          0          0          5          5          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_a.o
        48          6          0          0          0         96   _printf_char_common.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        22          0          0          0          0        100   _rserrno.o
        10          0          0          0          0         68   _sputc.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        22          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        40          6          0          0          0         84   noretval__2sprintf.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        24          0          0          0          0        164   basic.o
        98          4          0          0          0        140   d2f.o
       826         16          0          0          0        492   daddsub_clz.o
        16          4          0          0          0        116   dcheck1.o
        24          0          0          0          0        116   dcmpi.o
       688        140          0          0          0        256   ddiv.o
        84          0          0          0          0        232   dflt_clz.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       108          0          0          0          0        128   drleqf.o
       408         56          0          0          0        168   dsqrt_umaal.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
       100          0          0          0          0        116   retnan.o
        92          0          0          0          0        116   scalbn.o
        48          0          0          0          0        116   trapv.o
         0          0          0          0          0          0   usenofp.o
       164         44          0          0          0        620   dunder.o
        24          0          0          0          0        124   fabs.o
        48          0          0          0          0        124   fpclassify.o
       248          0          0          0          0        152   poly.o
      3152        296        136          0          0        352   pow.o
         0          0          8          0          0          0   qnan.o
       232          0          0          0          0        296   sqrt.o

    ----------------------------------------------------------------------
     12828        <USER>        <GROUP>          0        100       7992   Library Totals
        34          0          7          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      5528        202        231          0         96       2888   c_w.l
      3398        248          0          0          0       3436   fz_wm.l
      3868        340        144          0          0       1668   m_wm.l

    ----------------------------------------------------------------------
     12828        <USER>        <GROUP>          0        100       7992   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     24526       1898      17906        100    1187484     409969   Grand Totals
     24526       1898      17906        100    1187484     409969   ELF Image Totals
     24526       1898      17906        100          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                42432 (  41.44kB)
    Total RW  Size (RW Data + ZI Data)           1187584 (1159.75kB)
    Total ROM Size (Code + RO Data + RW Data)      42532 (  41.54kB)

==============================================================================

