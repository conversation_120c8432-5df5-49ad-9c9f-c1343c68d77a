/**
  ******************************************************************************
    @file    usbd_msc_core.h
    <AUTHOR> Application Team
    @version V1.1.0
    @date    19-March-2012
    @brief   header for the usbd_msc_core.c file
  ******************************************************************************
    @attention

    <h2><center>&copy; COPYRIGHT 2012 STMicroelectronics</center></h2>

    Licensed under MCD-ST Liberty SW License Agreement V2, (the "License");
    You may not use this file except in compliance with the License.
    You may obtain a copy of the License at:

           http://www.st.com/software_license_agreement_liberty_v2

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

  ******************************************************************************
*/

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef _USB_MSC_CORE_H_
#define _USB_MSC_CORE_H_

#include  "usbd_ioreq.h"

/** @addtogroup USBD_MSC_BOT
    @{
*/

/** @defgroup USBD_MSC
    @brief This file is the Header file for USBD_msc.c
    @{
*/


/** @defgroup USBD_BOT_Exported_Defines
    @{
*/


#define BOT_GET_MAX_LUN              0xFE
#define BOT_RESET                    0xFF
#define USB_MSC_CONFIG_DESC_SIZ      32

#define MSC_EPIN_SIZE                MSC_MAX_PACKET
#define MSC_EPOUT_SIZE               MSC_MAX_PACKET

/**
    @}
*/

/** @defgroup USB_CORE_Exported_Types
    @{
*/

extern USBD_Class_cb_TypeDef  USBD_MSC_cb;
/**
    @}
*/

/**
    @}
*/
#endif  // _USB_MSC_CORE_H_
/**
    @}
*/

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
