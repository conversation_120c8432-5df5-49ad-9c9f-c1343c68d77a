/**
  ******************************************************************************
    @file    usb_hcd.h
    <AUTHOR> Application Team
    @version V2.1.0
    @date    19-March-2012
    @brief   Host layer Header file
  ******************************************************************************
    @attention

    <h2><center>&copy; COPYRIGHT 2012 STMicroelectronics</center></h2>

    Licensed under MCD-ST Liberty SW License Agreement V2, (the "License");
    You may not use this file except in compliance with the License.
    You may obtain a copy of the License at:

           http://www.st.com/software_license_agreement_liberty_v2

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

  ******************************************************************************
*/

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __USB_HCD_H__
#define __USB_HCD_H__

/* Includes ------------------------------------------------------------------*/
#include "usb_regs.h"
#include "usb_core.h"


/** @addtogroup USB_OTG_DRIVER
    @{
*/

/** @defgroup USB_HCD
    @brief This file is the
    @{
*/


/** @defgroup USB_HCD_Exported_Defines
    @{
*/
/**
    @}
*/


/** @defgroup USB_HCD_Exported_Types
    @{
*/
/**
    @}
*/


/** @defgroup USB_HCD_Exported_Macros
    @{
*/
/**
    @}
*/

/** @defgroup USB_HCD_Exported_Variables
    @{
*/
/**
    @}
*/

/** @defgroup USB_HCD_Exported_FunctionsPrototype
    @{
*/
uint32_t  HCD_Init                 (USB_OTG_CORE_HANDLE *pdev,
                                    USB_OTG_CORE_ID_TypeDef coreID);
uint32_t  HCD_HC_Init              (USB_OTG_CORE_HANDLE *pdev,
                                    uint8_t hc_num);
uint32_t  HCD_SubmitRequest        (USB_OTG_CORE_HANDLE *pdev,
                                    uint8_t hc_num) ;
uint32_t  HCD_GetCurrentSpeed      (USB_OTG_CORE_HANDLE *pdev);
uint32_t  HCD_ResetPort            (USB_OTG_CORE_HANDLE *pdev);
uint32_t  HCD_IsDeviceConnected    (USB_OTG_CORE_HANDLE *pdev);
uint32_t  HCD_GetCurrentFrame      (USB_OTG_CORE_HANDLE *pdev) ;
URB_STATE HCD_GetURB_State         (USB_OTG_CORE_HANDLE *pdev,  uint8_t ch_num);
uint32_t  HCD_GetXferCnt           (USB_OTG_CORE_HANDLE *pdev,  uint8_t ch_num);
HC_STATUS HCD_GetHCState           (USB_OTG_CORE_HANDLE *pdev,  uint8_t ch_num) ;
/**
    @}
*/

#endif //__USB_HCD_H__


/**
    @}
*/

/**
    @}
*/
/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/

