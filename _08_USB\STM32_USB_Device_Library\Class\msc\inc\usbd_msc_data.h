/**
  ******************************************************************************
    @file    usbd_msc_data.h
    <AUTHOR> Application Team
    @version V1.1.0
    @date    19-March-2012
    @brief   header for the usbd_msc_data.c file
  ******************************************************************************
    @attention

    <h2><center>&copy; COPYRIGHT 2012 STMicroelectronics</center></h2>

    Licensed under MCD-ST Liberty SW License Agreement V2, (the "License");
    You may not use this file except in compliance with the License.
    You may obtain a copy of the License at:

           http://www.st.com/software_license_agreement_liberty_v2

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

  ******************************************************************************
*/

/* Define to prevent recursive inclusion -------------------------------------*/

#ifndef _USBD_MSC_DATA_H_
#define _USBD_MSC_DATA_H_

/* Includes ------------------------------------------------------------------*/
#include "usbd_conf.h"

/** @addtogroup STM32_USB_OTG_DEVICE_LIBRARY
    @{
*/

/** @defgroup USB_INFO
    @brief general defines for the usb device library file
    @{
*/

/** @defgroup USB_INFO_Exported_Defines
    @{
*/
#define MODE_SENSE6_LEN			 8
#define MODE_SENSE10_LEN		 8
#define LENGTH_INQUIRY_PAGE00		 7
#define LENGTH_FORMAT_CAPACITIES    	20

/**
    @}
*/


/** @defgroup USBD_INFO_Exported_TypesDefinitions
    @{
*/
/**
    @}
*/



/** @defgroup USBD_INFO_Exported_Macros
    @{
*/

/**
    @}
*/

/** @defgroup USBD_INFO_Exported_Variables
    @{
*/
extern const uint8_t MSC_Page00_Inquiry_Data[];
extern const uint8_t MSC_Mode_Sense6_data[];
extern const uint8_t MSC_Mode_Sense10_data[] ;

/**
    @}
*/

/** @defgroup USBD_INFO_Exported_FunctionsPrototype
    @{
*/

/**
    @}
*/

#endif /* _USBD_MSC_DATA_H_ */

/**
    @}
*/

/**
    @}
*/

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
